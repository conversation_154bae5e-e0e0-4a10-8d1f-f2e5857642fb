import os
import requests
import json
import msal  # pip install msal
import logging

# Set up logging for detailed troubleshooting
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# OAuth 2.0 Configuration
CLIENT_ID = "b06da978-4772-4428-b264-be848231b49f"  # Application (client) ID
CLIENT_SECRET = "****************************************"  # Client secret
TENANT_ID = "c2c833e7-5adb-44ea-9a1a-c82cbb4a9c94"  # Tenant ID
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"

# Use the application permissions scope
SCOPE = ["https://graph.microsoft.com/.default"]

# Create MSAL app instance
app = msal.ConfidentialClientApplication(
    CLIENT_ID,
    authority=AUTHORITY,
    client_credential=CLIENT_SECRET,
)

def get_access_token():
    """
    Get an access token for Microsoft Graph API using client credentials flow
    """
    logger.debug(f"Attempting to get access token with scopes: {SCOPE}")

    # Try to get token from cache first
    result = app.acquire_token_silent(SCOPE, account=None)

    if not result:
        logger.info("No suitable token in cache. Getting a new one...")
        result = app.acquire_token_for_client(scopes=SCOPE)
        logger.debug(f"Result from acquire_token_for_client: {result}")

    if "access_token" in result:
        logger.info("Access token acquired successfully!")
        token_preview = result["access_token"][:20] + "..." if result["access_token"] else "None"
        logger.debug(f"Token preview: {token_preview}")
        return result["access_token"]
    else:
        logger.error(f"Error acquiring token: {result.get('error')}")
        logger.error(f"Error description: {result.get('error_description')}")
        logger.error(f"Error correlation_id: {result.get('correlation_id')}")
        raise Exception(f"Failed to acquire token: {result.get('error_description')}")

def get_mail_user(access_token=None, email="<EMAIL>"):
    """
    Get a specific user by email address
    """
    if access_token is None:
        access_token = get_access_token()

    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    # Get specific user by email
    logger.debug(f"Looking up user with email: {email}")
    response = requests.get(f'https://graph.microsoft.com/v1.0/users?$filter=mail eq \'{email}\' or userPrincipalName eq \'{email}\'', headers=headers)

    if response.status_code == 200:
        users = response.json().get('value', [])
        if users:
            user = users[0]
            user_id = user.get('id')
            user_email = user.get('mail') or user.get('userPrincipalName')
            logger.info(f"Found user: {user_email} (ID: {user_id})")
            return user
        else:
            logger.error(f"No user found with email: {email}")
            raise Exception(f"No user found with email: {email}")
    else:
        logger.error(f"Failed to find user: {response.status_code}, {response.text}")
        raise Exception(f"Failed to find user: {response.status_code}, {response.text}")

def get_first_user(access_token=None):
    """
    Get the first user from the tenant with a valid mailbox for sending mail
    """
    if access_token is None:
        access_token = get_access_token()

    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    # Get all users without filtering - the 'ne null' filter is not supported
    logger.debug("Listing all users in the tenant")
    response = requests.get('https://graph.microsoft.com/v1.0/users', headers=headers)

    if response.status_code == 200:
        users = response.json().get('value', [])
        if users:
            # Find a user with a valid email address
            for user in users:
                if user.get('mail'):  # Only check for actual mail property
                    user_id = user.get('id')
                    user_email = user.get('mail')
                    logger.info(f"Selected user: {user_email} (ID: {user_id})")
                    return user
            
            logger.error("No users with valid email found in the tenant")
            raise Exception("No users with valid email found in the tenant")
        else:
            logger.error("No users found in the tenant")
            raise Exception("No users found in the tenant")
    else:
        logger.error(f"Failed to list users: {response.status_code}, {response.text}")
        raise Exception(f"Failed to get user profile: {response.status_code}, {response.text}")

def send_mail(recipient, subject, content, access_token=None, save_to_sent_items=True):
    """
    Send an email using Microsoft Graph API with application permissions
    """
    if access_token is None:
        access_token = get_access_token()

    # Since we're having permission issues with user lookup, let's try sending mail directly
    # using the /me endpoint or a shared mailbox approach
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    # Try to send mail using the /users/{id}/sendMail endpoint with a known user ID
    # This is a workaround since we can't query users properly
    
    # Option 1: Try with a hardcoded user ID if you know it
    # You can get this ID from the Azure Portal
    user_id = "b06da978-4772-4428-b264-be848231b49f"  # This is the app's object ID from the token
    user_email = "<EMAIL>"  # Hardcode the email address
    
    logger.info(f"Attempting to send email using hardcoded user ID: {user_id}")
    
    url = f'https://graph.microsoft.com/v1.0/users/{user_id}/sendMail'
    logger.info(f"Sending email via endpoint: {url}")
    
    # Create message payload with proper structure
    message = {
        "message": {
            "subject": subject,
            "body": {
                "contentType": "HTML",
                "content": content
            },
            "toRecipients": [
                {
                    "emailAddress": {
                        "address": recipient
                    }
                }
            ],
            # Explicitly set the sender
            "from": {
                "emailAddress": {
                    "address": user_email
                }
            }
        },
        "saveToSentItems": save_to_sent_items
    }
    
    payload = json.dumps(message)
    
    # Send request
    response = requests.post(url, headers=headers, data=payload)
    
  
def check_permissions(access_token=None):
    """
    Check what permissions the application has
    """
    if access_token is None:
        access_token = get_access_token()

    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    # Try to access various endpoints to check permissions
    endpoints = [
        ('https://graph.microsoft.com/v1.0/users', 'List all users'),
    ]

    logger.info("Checking application permissions...")

    for endpoint, description in endpoints:
        try:
            logger.info(f"Testing: {description} - {endpoint}")
            response = requests.get(endpoint, headers=headers)
            status = "✓" if response.status_code < 400 else "✗"
            logger.info(f"{status} {description}: Status {response.status_code}")

            if response.status_code < 400:
                logger.debug(f"Response: {response.text[:200]}...")
            else:
                logger.warning(f"Error: {response.text}")

        except Exception as e:
            logger.error(f"Exception testing {endpoint}: {str(e)}")

    # Get the first user to test user-specific endpoints
    try:
        user = get_first_user(access_token)
        user_id = user['id']
        
        # Test user-specific endpoints
        user_endpoints = [
            (f'https://graph.microsoft.com/v1.0/users/{user_id}/messages', 'Access user mailbox'),
            (f'https://graph.microsoft.com/v1.0/users/{user_id}/mailFolders', 'Access user mail folders'),
        ]
        
        for endpoint, description in user_endpoints:
            try:
                logger.info(f"Testing: {description} - {endpoint}")
                response = requests.get(endpoint, headers=headers)
                status = "✓" if response.status_code < 400 else "✗"
                logger.info(f"{status} {description}: Status {response.status_code}")
                
                if response.status_code >= 400:
                    logger.warning(f"Error: {response.text}")
            except Exception as e:
                logger.error(f"Exception testing {endpoint}: {str(e)}")
    
    except Exception as e:
        logger.error(f"Error getting users: {str(e)}")

    # Try to parse token claims to see permissions
    try:
        # Simple way to decode token without verification
        import base64
        import json
        
        # Split the token and get the payload part (second part)
        token_parts = access_token.split('.')
        if len(token_parts) >= 2:
            # Add padding if needed
            payload = token_parts[1]
            payload += '=' * ((4 - len(payload) % 4) % 4)
            
            # Decode the payload
            decoded_payload = base64.b64decode(payload).decode('utf-8')
            claims = json.loads(decoded_payload)
            
            logger.info("Token claims:")
            if 'roles' in claims:
                logger.info(f"Roles: {claims['roles']}")
                
                # Check for mail sending permission
                if 'Mail.Send' in claims['roles']:
                    logger.info("✓ Application has Mail.Send permission")
                else:
                    logger.warning("✗ Application does NOT have Mail.Send permission")
    except Exception as e:
        logger.error(f"Error decoding token: {str(e)}")

if __name__ == "__main__":
    try:
        # Get access token
        access_token = get_access_token()
        
        # Check permissions
        check_permissions(access_token)
        
        # Configure email details
        recipient = "<EMAIL>"  # Change to your test recipient
        subject = "Test Email from Microsoft Graph API"
        content = """
        <h2>This is a test email sent via Microsoft Graph API</h2>
        <p>If you received this email, the application is configured correctly!</p>
        <p>Sent using application permissions with Mail.Send scope.</p>
        """
        
        # Send email
        result = send_mail(recipient, subject, content, access_token)
        
        # Report result
        if result:
            print("✅ Email sent successfully!")
        else:
            print("❌ Failed to send email. Check logs for details.")
    
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        print(f"Error in main: {str(e)}")