import csv
import os
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

#logger

def log_rag_metrics_to_csv(
    query: str,
    vector_search_results: int,
    keyword_search_results: int,
    llm_reranker_results: int,
    chunk_scores: List[float],
    total_chunks_used: int,
    chunk_contexts: List[Tuple[str, str, float]],  # List of (text, source, score) tuples
    csv_file_path: Optional[str] = None
) -> str:
    """
    Log RAG search metrics to a CSV file.

    Args:
        query: The user query
        vector_search_results: Number of results from vector search
        keyword_search_results: Number of results from keyword search
        llm_reranker_results: Number of results after LLM reranking
        chunk_scores: List of scores for each chunk
        total_chunks_used: Total number of chunks used for the output
        chunk_contexts: List of tuples containing (text, source, score) for each chunk
        csv_file_path: Path to the CSV file (optional)

    Returns:
        Path to the CSV file
    """
    # If no file path is provided, create a default one in the logs directory
    if csv_file_path is None:
        # Create logs directory if it doesn't exist
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
        os.makedirs(logs_dir, exist_ok=True)

        # Create a filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d")
        csv_file_path = os.path.join(logs_dir, f"rag_metrics_{timestamp}.csv")

    # Check if file exists to determine if we need to write headers
    file_exists = os.path.isfile(csv_file_path)

    # Format chunk scores as a string
    chunk_scores_str = "|".join([f"{score:.4f}" for score in chunk_scores])

    # Format chunk contexts as a string
    # Each chunk is formatted as "TEXT [SOURCE] (SCORE)"
    # Chunks are separated by "|||"
    chunk_contexts_str = "|||".join([
        f"{text.replace('|||', ' ').replace('###', ' ')} [SOURCE: {source}] (SCORE: {score:.4f})"
        for text, source, score in chunk_contexts
    ])

    # Prepare the row data
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    row_data = {
        "timestamp": timestamp,
        "query": query,
        "vector_search_results": vector_search_results,
        "keyword_search_results": keyword_search_results,
        "llm_reranker_results": llm_reranker_results,
        "chunk_scores": chunk_scores_str,
        "total_chunks_used": total_chunks_used,
        "chunk_contexts": chunk_contexts_str
    }

    # Define the field names (column headers)
    fieldnames = [
        "timestamp",
        "query",
        "vector_search_results",
        "keyword_search_results",
        "llm_reranker_results",
        "chunk_scores",
        "total_chunks_used",
        "chunk_contexts"
    ]

    # Write to CSV file
    with open(csv_file_path, mode='a', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)

        # Write header if file doesn't exist
        if not file_exists:
            writer.writeheader()

        # Write the data row
        writer.writerow(row_data)

    print(f"[DEBUG] RAG metrics logged to {csv_file_path}")
    return csv_file_path
