from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaTask import LongRunningZairaTask

class SupervisorTask_Discord(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        split1 = input_message.split("___Call trace___:", 1)
        split2 = (split1[1].strip() if len(split1) > 1 else input_message).split("___Output message___:", 1)
        output_message = split2[1].strip() if len(split2) > 1 else ''
        user = await ZairaUserManager.find_user(state.user_guid)
        if user.my_task.calling_bot.name == "Discord":
            await user.my_task.send_response(output_message)

async def create_out_task_discord() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_Discord(name="discord_out",
            prompt="You're a task responsible when the output message needs to be directed to Discord."))
