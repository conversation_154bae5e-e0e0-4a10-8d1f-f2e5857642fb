# generated by datamodel-codegen:
#   filename:  airbyte_protocol.yaml

from __future__ import annotations

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union


class Type(Enum):
    RECORD = 'RECORD'
    STATE = 'STATE'
    LOG = 'LOG'
    SPEC = 'SPEC'
    CONNECTION_STATUS = 'CONNECTION_STATUS'
    CATALOG = 'CATALOG'
    TRACE = 'TRACE'
    CONTROL = 'CONTROL'


class Change(Enum):
    NULLED = 'NULLED'
    TRUNCATED = 'TRUNCATED'


class Reason(Enum):
    SOURCE_RECORD_SIZE_LIMITATION = 'SOURCE_RECORD_SIZE_LIMITATION'
    DESTINATION_RECORD_SIZE_LIMITATION = 'DESTINATION_RECORD_SIZE_LIMITATION'
    PLATFORM_RECORD_SIZE_LIMITATION = 'PLATFORM_RECORD_SIZE_LIMITATION'
    SOURCE_FIELD_SIZE_LIMITATION = 'SOURCE_FIELD_SIZE_LIMITATION'
    DESTINATION_FIELD_SIZE_LIMITATION = 'DESTINATION_FIELD_SIZE_LIMITATION'
    PLATFORM_FIELD_SIZE_LIMITATION = 'PLATFORM_FIELD_SIZE_LIMITATION'
    SOURCE_SERIALIZATION_ERROR = 'SOURCE_SERIALIZATION_ERROR'
    DESTINATION_SERIALIZATION_ERROR = 'DESTINATION_SERIALIZATION_ERROR'
    PLATFORM_SERIALIZATION_ERROR = 'PLATFORM_SERIALIZATION_ERROR'
    SOURCE_RETRIEVAL_ERROR = 'SOURCE_RETRIEVAL_ERROR'
    DESTINATION_TYPECAST_ERROR = 'DESTINATION_TYPECAST_ERROR'


@dataclass
class AirbyteRecordMessageMetaChange:
    field: str
    change: Change
    reason: Reason


class AirbyteStateType(Enum):
    GLOBAL = 'GLOBAL'
    STREAM = 'STREAM'
    LEGACY = 'LEGACY'


@dataclass
class StreamDescriptor:
    name: str
    namespace: Optional[str] = None


@dataclass
class AirbyteStateBlob:
    pass


@dataclass
class AirbyteStateStats:
    recordCount: Optional[float] = None


class Level(Enum):
    FATAL = 'FATAL'
    ERROR = 'ERROR'
    WARN = 'WARN'
    INFO = 'INFO'
    DEBUG = 'DEBUG'
    TRACE = 'TRACE'


@dataclass
class AirbyteLogMessage:
    level: Level
    message: str
    stack_trace: Optional[str] = None


class TraceType(Enum):
    ERROR = 'ERROR'
    ESTIMATE = 'ESTIMATE'
    STREAM_STATUS = 'STREAM_STATUS'
    ANALYTICS = 'ANALYTICS'


class FailureType(Enum):
    system_error = 'system_error'
    config_error = 'config_error'
    transient_error = 'transient_error'


@dataclass
class AirbyteErrorTraceMessage:
    message: str
    internal_message: Optional[str] = None
    stack_trace: Optional[str] = None
    failure_type: Optional[FailureType] = None
    stream_descriptor: Optional[StreamDescriptor] = None


class EstimateType(Enum):
    STREAM = 'STREAM'
    SYNC = 'SYNC'


@dataclass
class AirbyteEstimateTraceMessage:
    name: str
    type: EstimateType
    namespace: Optional[str] = None
    row_estimate: Optional[int] = None
    byte_estimate: Optional[int] = None


class AirbyteStreamStatus(Enum):
    STARTED = 'STARTED'
    RUNNING = 'RUNNING'
    COMPLETE = 'COMPLETE'
    INCOMPLETE = 'INCOMPLETE'


class AirbyteStreamStatusReasonType(Enum):
    RATE_LIMITED = 'RATE_LIMITED'


@dataclass
class AirbyteStreamStatusRateLimitedReason:
    quota_reset: Optional[int] = None


@dataclass
class AirbyteStreamStatusReason:
    type: AirbyteStreamStatusReasonType
    rate_limited: Optional[AirbyteStreamStatusRateLimitedReason] = None


@dataclass
class AirbyteStreamStatusTraceMessage:
    stream_descriptor: StreamDescriptor
    status: AirbyteStreamStatus
    reasons: Optional[List[AirbyteStreamStatusReason]] = None


@dataclass
class AirbyteAnalyticsTraceMessage:
    type: str
    value: Optional[str] = None


class OrchestratorType(Enum):
    CONNECTOR_CONFIG = 'CONNECTOR_CONFIG'


@dataclass
class AirbyteControlConnectorConfigMessage:
    config: Dict[str, Any]


class Status(Enum):
    SUCCEEDED = 'SUCCEEDED'
    FAILED = 'FAILED'


@dataclass
class AirbyteConnectionStatus:
    status: Status
    message: Optional[str] = None


class SyncMode(Enum):
    full_refresh = 'full_refresh'
    incremental = 'incremental'


class DestinationSyncMode(Enum):
    append = 'append'
    overwrite = 'overwrite'
    append_dedup = 'append_dedup'


@dataclass
class OAuth2Specification:
    rootObject: Optional[List[Union[str, int]]] = None
    oauthFlowInitParameters: Optional[List[List[str]]] = None
    oauthFlowOutputParameters: Optional[List[List[str]]] = None


class AuthType(Enum):
    oauth2_0 = 'oauth2.0'


@dataclass
class AuthSpecification:
    auth_type: Optional[AuthType] = None
    oauth2Specification: Optional[OAuth2Specification] = None


class AuthFlowType(Enum):
    oauth2_0 = 'oauth2.0'
    oauth1_0 = 'oauth1.0'


@dataclass
class State:
    min: Optional[int] = None
    max: Optional[int] = None


@dataclass
class OauthConnectorInputSpecification:
    consent_url: str
    access_token_url: str
    scope: Optional[str] = None
    access_token_headers: Optional[Dict[str, Any]] = None
    access_token_params: Optional[Dict[str, Any]] = None
    extract_output: Optional[List[str]] = None
    state: Optional[State] = None
    client_id_key: Optional[str] = None
    client_secret_key: Optional[str] = None
    scope_key: Optional[str] = None
    state_key: Optional[str] = None
    auth_code_key: Optional[str] = None
    redirect_uri_key: Optional[str] = None
    token_expiry_key: Optional[str] = None


@dataclass
class OAuthConfigSpecification:
    oauth_user_input_from_connector_config_specification: Optional[Dict[str, Any]] = (
        None
    )
    oauth_connector_input_specification: Optional[OauthConnectorInputSpecification] = (
        None
    )
    complete_oauth_output_specification: Optional[Dict[str, Any]] = None
    complete_oauth_server_input_specification: Optional[Dict[str, Any]] = None
    complete_oauth_server_output_specification: Optional[Dict[str, Any]] = None


@dataclass
class AirbyteRecordMessageMeta:
    changes: Optional[List[AirbyteRecordMessageMetaChange]] = None


@dataclass
class AirbyteStreamState:
    stream_descriptor: StreamDescriptor
    stream_state: Optional[AirbyteStateBlob] = None


@dataclass
class AirbyteGlobalState:
    stream_states: List[AirbyteStreamState]
    shared_state: Optional[AirbyteStateBlob] = None


@dataclass
class AirbyteTraceMessage:
    type: TraceType
    emitted_at: float
    error: Optional[AirbyteErrorTraceMessage] = None
    estimate: Optional[AirbyteEstimateTraceMessage] = None
    stream_status: Optional[AirbyteStreamStatusTraceMessage] = None
    analytics: Optional[AirbyteAnalyticsTraceMessage] = None


@dataclass
class AirbyteControlMessage:
    type: OrchestratorType
    emitted_at: float
    connectorConfig: Optional[AirbyteControlConnectorConfigMessage] = None


@dataclass
class AirbyteStream:
    name: str
    json_schema: Dict[str, Any]
    supported_sync_modes: List[SyncMode]
    source_defined_cursor: Optional[bool] = None
    default_cursor_field: Optional[List[str]] = None
    source_defined_primary_key: Optional[List[List[str]]] = None
    namespace: Optional[str] = None
    is_resumable: Optional[bool] = None


@dataclass
class ConfiguredAirbyteStream:
    stream: AirbyteStream
    sync_mode: SyncMode
    destination_sync_mode: DestinationSyncMode
    cursor_field: Optional[List[str]] = None
    primary_key: Optional[List[List[str]]] = None
    generation_id: Optional[int] = None
    minimum_generation_id: Optional[int] = None
    sync_id: Optional[int] = None


@dataclass
class AdvancedAuth:
    auth_flow_type: Optional[AuthFlowType] = None
    predicate_key: Optional[List[str]] = None
    predicate_value: Optional[str] = None
    oauth_config_specification: Optional[OAuthConfigSpecification] = None


@dataclass
class ConnectorSpecification:
    connectionSpecification: Dict[str, Any]
    documentationUrl: Optional[str] = None
    changelogUrl: Optional[str] = None
    supportsIncremental: Optional[bool] = None
    supportsNormalization: Optional[bool] = False
    supportsDBT: Optional[bool] = False
    supported_destination_sync_modes: Optional[List[DestinationSyncMode]] = None
    authSpecification: Optional[AuthSpecification] = None
    advanced_auth: Optional[AdvancedAuth] = None
    protocol_version: Optional[str] = None


@dataclass
class AirbyteRecordMessage:
    stream: str
    data: Dict[str, Any]
    emitted_at: int
    namespace: Optional[str] = None
    meta: Optional[AirbyteRecordMessageMeta] = None


@dataclass
class AirbyteStateMessage:
    type: Optional[AirbyteStateType] = None
    stream: Optional[AirbyteStreamState] = None
    global_: Optional[AirbyteGlobalState] = None
    data: Optional[Dict[str, Any]] = None
    sourceStats: Optional[AirbyteStateStats] = None
    destinationStats: Optional[AirbyteStateStats] = None


@dataclass
class AirbyteCatalog:
    streams: List[AirbyteStream]


@dataclass
class ConfiguredAirbyteCatalog:
    streams: List[ConfiguredAirbyteStream]


@dataclass
class AirbyteMessage:
    type: Type
    log: Optional[AirbyteLogMessage] = None
    spec: Optional[ConnectorSpecification] = None
    connectionStatus: Optional[AirbyteConnectionStatus] = None
    catalog: Optional[AirbyteCatalog] = None
    record: Optional[AirbyteRecordMessage] = None
    state: Optional[AirbyteStateMessage] = None
    trace: Optional[AirbyteTraceMessage] = None
    control: Optional[AirbyteControlMessage] = None


@dataclass
class AirbyteProtocol:
    airbyte_message: Optional[AirbyteMessage] = None
    configured_airbyte_catalog: Optional[ConfiguredAirbyteCatalog] = None
