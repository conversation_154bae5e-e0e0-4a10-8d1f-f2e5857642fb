from imports import *

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from endpoints.oauth_endpoint import OAuth2Verifier, OAuth2App

async def oauth_handle_commands(self: "OAuth2Verifier", app: "OAuth2App", tokens):
    ret_val = True
    if len(app.commands) > 0:
        for command in app.commands:
            if command == "exit":
                if Globals.is_docker():
                    async def delayed_exit():
                        from asyncio import sleep
                        await sleep(15)
                        exit()

                    from asyncio import create_task
                    create_task(delayed_exit())
                    ret_val = False
            elif command == "crawl":
                # Crawl the user's website in case it changed
                site_url = await self.get_token("website")
                if site_url != "":
                    print("Starting site-crawl")
                    from subprocess import run as subprocess_run
                    result = subprocess_run('cmd /c playwright install', shell=True, capture_output=True, text=True)
                    from inputs.crawler import Crawler
                    # Initialize the crawler
                    crawler = await <PERSON><PERSON><PERSON>.setup_async()
                    sitemap = await self.get_token("website", "refresh_token")
                    if sitemap == "":
                        # Test with a single URL
                        await crawler.crawl(site_url, is_sitemap=False)
                    else:
                        # Test with a sitemap
                        sitemap_url = f"{site_url.rstrip('/')}/{sitemap}"
                        await crawler.crawl(sitemap_url, is_sitemap=True, max_concurrent=5)
                    # Clean up
                    await crawler.close_crawler()
                    print("Finished site-crawl")
            elif command == "debug":
                debug_modus = await self.get_token("debug")
                company_data = await self.get_token("debug", "refresh_token")
                Globals.set_debug("j" in debug_modus and "j" in company_data)
                # Set variable based on company_data==True
            elif command == "extract_woocommerce":
                # Extract WooCommerce data and store in vector database
                print("Starting WooCommerce data extraction")
                try:
                    from inputs.woocommerce_extractor import WooCommerceExtractor

                    # Get WooCommerce credentials from stored tokens
                    consumer_key = await self.get_token("woocommerce", "access_token")
                    consumer_secret = await self.get_token("woocommerce", "refresh_token")
                    site_url = await self.get_token("woocommerce", "token_type")
                    start_date = await self.get_token("woocommerce", "str1")

                    from datetime import datetime
                    start_date = datetime.strptime(start_date, "%Y-%m-%d")

                    print(f"WooCommerce credentials check:")
                    print(f"- Consumer Key: {'✓' if consumer_key else '✗'}")
                    print(f"- Consumer Secret: {'✓' if consumer_secret else '✗'}")
                    print(f"- Site URL: {site_url if site_url else '✗'}")
                    print(f"- Start Date: {start_date.date().isoformat() if start_date else '✗'}")

                    if consumer_key and consumer_secret and site_url:
                        # Initialize the WooCommerce extractor
                        extractor = await WooCommerceExtractor.setup_async(
                            consumer_key=consumer_key,
                            consumer_secret=consumer_secret,
                            site_url=site_url,
                            start_date=start_date
                        )

                        # Extract and process WooCommerce data
                        extraction_success = await extractor.extract_and_store_data()

                        if extraction_success:
                            print(" WooCommerce data extraction completed successfully")
                        else:
                            print(" WooCommerce data extraction failed")
                            print("Check the logs above for detailed error information")
                            # Don't set ret_val = False here as this might be due to Meltano setup issues
                            # The OAuth process itself was successful

                        print("Finished WooCommerce data extraction")
                    else:
                        print("Missing WooCommerce credentials")
                        print("Please ensure all required fields are filled:")
                        print("- Consumer Key, Consumer Secret, WooCommerce URL, Start Date")
                        ret_val = False
                except Exception as e:
                    print(f" Error during WooCommerce extraction: {e}")
                    import traceback
                    traceback.print_exc()
                    # Don't set ret_val = False for extraction errors, only for OAuth/credential errors
                    print("OAuth process completed, but extraction encountered issues")
    return ret_val