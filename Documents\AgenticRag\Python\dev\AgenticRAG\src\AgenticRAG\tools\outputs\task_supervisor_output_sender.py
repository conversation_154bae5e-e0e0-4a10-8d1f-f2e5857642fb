from imports import *

from langgraph.graph import END

from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor
from tools.outputs.output_tasks.task_out_discord import create_out_task_discord
from tools.outputs.output_tasks.task_out_teams import create_out_task_teams
from tools.outputs.output_tasks.task_out_python import create_out_task_python
from tools.outputs.output_tasks.task_out_http import create_out_task_http
from tools.outputs.output_tasks.task_out_whatsapp import create_out_task_whatsapp


# Define tools for agents

async def create_supervisor_output_sender() -> SupervisorSupervisor:
    class TaskCreator:

        async def create_tasks(self):
            self.discord_task = await create_out_task_discord()
            self.teams_task = await create_out_task_teams()
            self.python_task = await create_out_task_python()
            self.http_task = await create_out_task_http()
            self.whatsapp_task = await create_out_task_whatsapp()


        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(name="output_sender_supervisor", prompt=
                f"You are a supervisor responsible for sending an output message that has already beent sent to an output task and potentially needs to be sent to a second output task."
                 " You will only respond with a task if the ___Output message___ calls for it and only to cover rare use cases."
                 " Under NO circumstance are you allowed to respond with the task mentioned in ___Original source___ as that was already called prior to your supervision."
                 " Check the output message to determine if an additional destination has a high probability of needing called."
                f" In most normal cases you will respond with {END} ."
                 " You must also respond with {END} if you have already triggered a task through your supervision."
                #" If it's unlikely that any of your remaining tasks are related to the HumanMessage, respond with {END} instead of a task name."
                )) \
                .add_task(self.discord_task) \
                .add_task(self.teams_task) \
                .add_task(self.python_task) \
                .add_task(self.http_task) \
                .add_task(self.whatsapp_task) \
                .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
