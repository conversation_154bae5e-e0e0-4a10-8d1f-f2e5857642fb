from imports import *

from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorTask_SingleAgent
from tools.outputs.output_processing.task_out_language_verifier import create_out_processing_language_verifier

# Define tools for agents

async def create_supervisor_output_processing() -> SupervisorSupervisor:
    class TaskCreator:
        language_verifier: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.language_verifier = await create_out_processing_language_verifier()

        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(name="output_processing_supervisor", prompt=
                f"You are an output supervisor processing a message that originated from a single Source. "
                 "Based on the 'original input', determine if the 'Output message' needs any additional changes. "
                 "Most importantly, ensure the output is in the same language as the original input. "
                 "Use the language_verifier task to verify and translate if needed.")) \
                .compile()
                #.add_task(self.language_verifier, priority=1) \
                
    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
