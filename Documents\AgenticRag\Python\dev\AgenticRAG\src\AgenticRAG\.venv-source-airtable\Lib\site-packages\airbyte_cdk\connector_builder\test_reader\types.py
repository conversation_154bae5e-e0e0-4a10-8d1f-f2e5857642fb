#
# Copyright (c) 2023 Airbyte, Inc., all rights reserved.
#

"""
This module defines type aliases utilized in the Airbyte Connector Builder's test reader.
These aliases streamline type-checking for heterogeneous message groups and schema outputs,
ensuring consistency throughout the processing of stream data and associated messages.

Type Aliases:
    MESSAGE_GROUPS:
        An iterable union of message-like objects which may include:
            - StreamReadSlices: Represents slices used to read data from a stream.
            - AirbyteControlMessage: Represents control commands used in the Airbyte protocol.
            - AirbyteLogMessage: Represents log messages generated by the system.
            - AirbyteTraceMessage: Represents trace messages typically used for debugging.
            - AuxiliaryRequest: Represents any supplementary request issued during processing.

    INFERRED_SCHEMA_OUTPUT_TYPE:
        A tuple where:
            - The first element is either an InferredSchema instance or None, denoting the inferred JSON schema.
            - The second element is a list of LogMessage instances capturing logs produced during inference.

    GROUPED_MESSAGES:
        A tuple representing grouped messages divided as follows:
            - A list of StreamReadSlices.
            - A list of LogMessage instances.
            - A list of AuxiliaryRequest instances.
            - An optional AirbyteControlMessage that, if present, governs control flow in message processing.
"""

from typing import Any, Iterable, List

from airbyte_cdk.connector_builder.models import (
    AuxiliaryRequest,
    HttpRequest,
    HttpResponse,
    LogMessage,
    StreamReadSlices,
)
from airbyte_cdk.models import (
    AirbyteControlMessage,
    AirbyteLogMessage,
    AirbyteTraceMessage,
)
from airbyte_cdk.utils.schema_inferrer import (
    InferredSchema,
)

MESSAGE_GROUPS = Iterable[
    StreamReadSlices
    | AirbyteControlMessage
    | AirbyteLogMessage
    | AirbyteTraceMessage
    | AuxiliaryRequest,
]

INFERRED_SCHEMA_OUTPUT_TYPE = tuple[
    InferredSchema | None,
    List[LogMessage],
]

GROUPED_MESSAGES = tuple[
    List[StreamReadSlices],
    List[LogMessage],
    List[AuxiliaryRequest],
    AirbyteControlMessage | None,
]

LOG_MESSAGES_OUTPUT_TYPE = tuple[
    bool,
    HttpRequest | None,
    HttpResponse | None,
    AuxiliaryRequest | None,
    AirbyteLogMessage | None,
]

ASYNC_AUXILIARY_REQUEST_TYPES = [
    "ASYNC_CREATE",
    "ASYNC_POLL",
    "ASYNC_ABORT",
    "ASYNC_DELETE",
]
