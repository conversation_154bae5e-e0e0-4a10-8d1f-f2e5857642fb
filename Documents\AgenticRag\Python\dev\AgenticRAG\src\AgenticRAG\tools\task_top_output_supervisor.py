from imports import *

from langgraph.types import Command
from langgraph.graph import END
from langchain_core.messages import HumanMessage, SystemMessage

from managers.manager_supervisors import Supervisor<PERSON>anager, SupervisorSupervisor, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from tools.outputs.task_supervisor_output_sender import create_supervisor_output_sender
from tools.outputs.task_supervisor_output_processing import create_supervisor_output_processing

class SupervisorTopOutputSupervisor(SupervisorSupervisor):
    _instance = None
    _initialized = False

    output_converter_supervisor: SupervisorSupervisor = None
    output_sender_supervisor: SupervisorSupervisor = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.__init__()
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    def __init__(self):
        super().__init__(name="top_output_supervisor", prompt="")
        # Additional initialization if needed

    @classmethod
    async def create_small_tasks(cls):
        pass
        
    @classmethod
    async def create_supervisors(cls):
        instance = cls.get_instance()
        instance.output_converter_supervisor = await create_supervisor_output_processing()
        instance.output_sender_supervisor = await create_supervisor_output_sender()
        
    @classmethod
    async def create_top_output_supervisor(cls) -> SupervisorSupervisor:
        instance = cls.get_instance()
        await instance.create_small_tasks()
        await instance.create_supervisors()

        cls._instance = SupervisorManager.register_supervisor(SupervisorTopOutputSupervisor()) \
            .add_task(instance.output_converter_supervisor) \
            .add_task(instance.output_sender_supervisor) \
            .compile()
        
    async def llm_call_router(self, state: SupervisorTaskState):
        user = await ZairaUserManager.find_user(state.user_guid)
        result1 = (await self.output_converter_supervisor.call_supervisor(f"Process the following output message. {state.original_input}", user))
        new_input = result1["result"].replace("Process the following output message. ", "", 1)
        continue_question = await ZairaSettings.llm.ainvoke(
            input=[
                SystemMessage("Try and find a question that would be likely to help the user in his search for his yet-to-be-requested next prompt or problem."),
                HumanMessage(new_input)
            ]
        )

        response = new_input + "\n\n" + continue_question.content

        split1 = response.split("___Original source___:", 1)
        if len(split1) > 1:
            split2 = split1[1].split(".", 1)
            if len(split2) > 0:
                output_task = SupervisorManager.get_task(f"{split2[0].lower().strip()}_out")
                state.messages.append(HumanMessage(response))
                await output_task.llm_call(state)
            else:
                print(f"No proper output found for {split1[1]}")
        else:
            print(f"No proper output found for {response}")

        result2 = (await self.output_sender_supervisor.call_supervisor(f"Route the following '___Output message___' only to the likely outputs. {response}", user))
        call_trace = [f"{self.name}: llm_call converter + sender"] + result1["call_trace"] + result2["call_trace"] + [f"{self.name}: end llm_call converter + sender"]

        LogFire.log("OUTPUT", f"Task finished with question of length: {len(response)}.\nCall trace: \n" + "\n".join(call_trace), f"Question: {response}" if not Globals.get_debug() else "", user, "top_output")
        return Command(update={"messages": [new_input], "call_trace": call_trace}, goto=END)

async def create_top_output_supervisor() -> SupervisorSupervisor:
    return await SupervisorTopOutputSupervisor.create_top_output_supervisor()
    