    <script>
        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Initialize particles
        createParticles();

        // Password visibility toggle
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleButton = document.querySelector('.password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleButton.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleButton.textContent = '👁️';
            }
        }

		// Login form handling
		document.getElementById('loginForm').addEventListener('submit', async function(e) {
			e.preventDefault();
			
			const username = document.getElementById('username').value;
			const password = document.getElementById('password').value;
			const button = document.getElementById('loginButton');
			const buttonText = button.querySelector('.button-text');
			const errorMessage = document.getElementById('errorMessage');
			
			// Hide error message
			errorMessage.classList.remove('show');
			
			// Show loading state
			button.classList.add('button-loading');
			button.disabled = true;
			buttonText.textContent = 'Controleren...';
			
			try {
				// First validate credentials via fetch (without navigation)
				const validateResponse = await fetch('/validate-login', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						username: username,
						password: password
					})
				});
				
				const validateData = await validateResponse.json();
				
				if (validateResponse.ok && validateData.success) {
					buttonText.textContent = 'Succesvol!';
					
					// Create and submit a form to trigger browser password manager
					const form = document.createElement('form');
					form.method = 'POST';
					form.action = '/dashboard';
					
					// Add username field
					const usernameInput = document.createElement('input');
					usernameInput.type = 'hidden';
					usernameInput.name = 'username';
					usernameInput.value = username;
					form.appendChild(usernameInput);
					
					// Add password field
					const passwordInput = document.createElement('input');
					passwordInput.type = 'hidden';
					passwordInput.name = 'password';
					passwordInput.value = password;
					form.appendChild(passwordInput);
					
					// Submit form (this will navigate to dashboard)
					document.body.appendChild(form);
					form.submit();
					
				} else {
					// Show error message from server or default
					errorMessage.textContent = validateData.message || 'Onjuist wachtwoord';
					errorMessage.classList.add('show');
					button.classList.remove('button-loading');
					button.disabled = false;
					buttonText.textContent = 'Inloggen';
					
					// Clear password field
					document.getElementById('password').value = '';
					document.getElementById('password').focus();
				}
			} catch (error) {
				console.error('Login error:', error);
				
				// Show network error
				errorMessage.textContent = 'Verbindingsfout. Probeer opnieuw.';
				errorMessage.classList.add('show');
				button.classList.remove('button-loading');
				button.disabled = false;
				buttonText.textContent = 'Inloggen';
				
				// Clear password field
				document.getElementById('password').value = '';
				document.getElementById('password').focus();
			}
		});

        // Auto-focus password field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // Enter key handling
        document.getElementById('username').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
				document.getElementById('password').focus();
            }
        });

        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });

        // Forgot password handler
        function showForgotPassword() {
            alert('Neem contact op met de beheerder voor wachtwoord reset.');
        }

        // Add subtle animation to card on load
        window.addEventListener('load', function() {
            const card = document.querySelector('.login-card');
            card.style.transform = 'translateY(20px)';
            card.style.opacity = '0';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.transform = 'translateY(0)';
                card.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
