# generated by datamodel-codegen:
#   filename:  well_known_types.yaml

from __future__ import annotations

from enum import Enum
from typing import Any, Union

Model = Any


String = str


BinaryData = str


Date = str


TimestampWithTimezone = str


TimestampWithoutTimezone = str


TimeWithTimezone = str


TimeWithoutTimezone = str


class Number1(Enum):
    Infinity = 'Infinity'
    field_Infinity = '-Infinity'
    NaN = 'NaN'


Number = Union[str, Number1]


class Integer1(Enum):
    Infinity = 'Infinity'
    field_Infinity = '-Infinity'
    NaN = 'NaN'


Integer = Union[str, Integer1]


Boolean = bool
