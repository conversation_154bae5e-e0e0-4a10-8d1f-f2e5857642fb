# PyPI dependencies with version constraints
llama-index-core>=0.12.19,<0.13.0
transformers>=4.36.0,<4.49.0
optimum[onnx]>=1.24.0,<2.0.0
llama-index-llms-ollama>=0.5.2,<0.6.0
llama-index-embeddings-huggingface>=0.4.0,<0.5.0
llama-index-embeddings-huggingface-optimum>=0.3.0,<0.4.0
llama-index-readers-file>=0.4.5,<0.5.0
pydantic>=2.0.0
sentence-transformers>=3.4.1,<4.0.0
qdrant-client>=1.13.3,<2.0.0
llama-index-vector-stores-qdrant>=0.4.3,<0.5.0
llama-index-embeddings-fastembed>=0.3.0,<0.4.0
unstructured[all-docs]>=0.16.25,<0.17.0
poppler-utils>=0.1.0,<0.2.0
libmagic>=1.0,<2.0
pandoc>=2.4,<3.0
tesseract>=0.1.3,<0.2.0
flask[async]>=3.1.0,<4.0.0
agno>=1.1.12,<2.0.0
psutil==7.0.0
crawl4ai>=0.5.0.post4,<0.6.0
langgraph>=0.3.11,<0.4.0
langchainhub>=0.1.21,<0.2.0
logfire[flask]>=3.9.0,<4.0.0
langchain-community[tools-tavily-search]>=0.3.20,<0.4.0
tavily-python>=0.5.1,<0.6.0
langgraph-checkpoint-sqlite>=2.0.6,<3.0.0
discord>=2.3.2,<3.0.0
playwright>=1.51.0,<2.0.0
langchain>=0.3.21,<0.4.0
langchain-openai>=0.3.11,<0.4.0
langchain-experimental>=0.3.4,<0.4.0
ipython>=9.0.2,<10.0.0
langgraph-supervisor>=0.0.16,<0.0.17
resend>=2.7.0,<3.0.0
toml>=0.10.2,<0.11.0
asyncpg>=0.30.0,<0.31.0
llama-index-llms-openai>=0.3.37,<0.4.0
botbuilder-core>=4.16.2,<5.0.0
llama-index-llms-langchain>=0.6.1,<0.7.0
langchain-ollama>=0.3.2,<0.4.0
langchain-text-splitters>=0.3.0,<0.4.0
aiohttp-oauth2-client>=1.0.2,<2.0.0
slack-sdk>=3.35.0,<4.0.0
aioconsole>=0.8.1,<0.9.0
msal>=1.32.3,<2.0.0
google-api-python-client>=2.169.0,<3.0.0
imutils>=0.5.4,<0.6.0
pytesseract>=0.3.13,<0.4.0
python-dotenv>=1.1.0,<2.0.0
python-magic>=0.4.27,<10.0.0
