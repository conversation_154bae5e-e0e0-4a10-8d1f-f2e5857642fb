timestamp,query,vector_search_results,keyword_search_results,llm_reranker_results,chunk_scores,total_chunks_used,chunk_contexts
2025-05-29 11:36:36,Who is annie gray,40,7,0,,0,
2025-05-29 11:40:09,who is da<PERSON>,40,7,1,1.0000,1,"* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 1.0000)"
2025-05-29 11:53:47,Who is da<PERSON> van <PERSON>,40,7,3,2.0000|2.0000|2.0000,3,"* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 2.0000)|||**Voorbereiding en planning:**     * Inventarisatie van de te koppelen systemen en databronnen.     * Vaststellen van gebruikersrollen en toegangsrechten.     * Opstellen van een projectplan met mijlpalen en deliverables.  2. **Technische setup en datakoppelingen:**     * Inrichten van verbindingen met uw cloudapplicaties (CRM, ERP, SharePoint, Google Drive, etc.).     * Configuratie van de tools aan Askzaira.  3. **Gebruikerstraining en livegang:**     * workshop (online of op locatie) om me... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 2.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 2.0000)"
2025-05-29 13:38:25,Wanneer is de opdracht succesvol afgerond?,40,7,7,10.0000|10.0000|10.0000|10.0000|8.0000|8.0000|7.0000,7,"**type**: NarrativeText  **id**: 5b619bd99249b7faedaa3419b8271c4e  **text**: De opdracht is succesvol afgerond wanneer:  **metadata**: {'coordinates': {'points': ((70.824, 474.90999999999997), (70.824, 485.94999999999993), (269.32504, 485.94999999999993), (269.32504, 474.90999999999997)), 'system': 'PixelSpace', 'layout_width': 595.32, 'layout_height': 841.92}, 'file_directory': 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG/src/meltano/output', 'filename': 'Stageopdrachtom... [SOURCE: Unknown] (SCORE: 10.0000)|||**type**: NarrativeText  **id**: 5b619bd99249b7faedaa3419b8271c4e  **text**: De opdracht is succesvol afgerond wanneer:  **metadata**: {'coordinates': {'points': ((70.824, 474.90999999999997), (70.824, 485.94999999999993), (269.32504, 485.94999999999993), (269.32504, 474.90999999999997)), 'system': 'PixelSpace', 'layout_width': 595.32, 'layout_height': 841.92}, 'file_directory': 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG/src/meltano/output', 'filename': 'Stageopdrachtom... [SOURCE: Unknown] (SCORE: 10.0000)|||**type**: NarrativeText  **id**: 5b619bd99249b7faedaa3419b8271c4e  **text**: De opdracht is succesvol afgerond wanneer:  **metadata**: {'coordinates': {'points': ((70.824, 474.90999999999997), (70.824, 485.94999999999993), (269.32504, 485.94999999999993), (269.32504, 474.90999999999997)), 'system': 'PixelSpace', 'layout_width': 595.32, 'layout_height': 841.92}, 'file_directory': 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG/src/meltano/output', 'filename': 'Stageopdrachtom... [SOURCE: Unknown] (SCORE: 10.0000)|||**type**: NarrativeText  **id**: 5b619bd99249b7faedaa3419b8271c4e  **text**: De opdracht is succesvol afgerond wanneer:  **metadata**: {'coordinates': {'points': ((70.824, 474.90999999999997), (70.824, 485.94999999999993), (269.32504, 485.94999999999993), (269.32504, 474.90999999999997)), 'system': 'PixelSpace', 'layout_width': 595.32, 'layout_height': 841.92}, 'file_directory': 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG/src/meltano/output', 'filename': 'Stageopdrachtom... [SOURCE: Unknown] (SCORE: 10.0000)|||**type**: Title  **id**: 10f9c0b57570cdc79858d9d859a2a62c  **text**: Doel van de opdracht  **metadata**: {'coordinates': {'points': ((70.824, 307.97), (70.824, 319.01), (170.78503999999998, 319.01), (170.78503999999998, 307.97)), 'system': 'PixelSpace', 'layout_width': 595.32, 'layout_height': 841.92}, 'file_directory': 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticRAG/src/meltano/output', 'filename': 'Stageopdrachtomschrijving v1.2 simonboot ecotax filtertechniek.pdf', 'langu... [SOURCE: Unknown] (SCORE: 8.0000)|||**type**: ListItem  **id**: 4bb92afd1f13559d134ec4330aaa50e8  **text**: 3. Alle technische berekeningen (zoals assterkte en aandrijvingsspecificaties) correct zijn uitgevoerd en gedocumenteerd.  **metadata**: {'coordinates': {'points': ((88.824, 570.9484), (88.824, 596.8299999999999), (490.47104, 596.8299999999999), (490.47104, 570.9484)), 'system': 'PixelSpace', 'layout_width': 595.32, 'layout_height': 841.92}, 'file_directory': 'C:\\Users\\<USER>\\Documents\\AgenticRag\\Python\\dev\\AgenticR... [SOURCE: Unknown] (SCORE: 8.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 7.0000)"
2025-05-29 14:51:14,Waar zijn bandfilters handig voor?,32,7,7,9.0000|9.0000|9.0000|9.0000|8.0000|8.0000|7.0000,7,"Stageopdracht: Ontwikkeling van een Bandfiltermachine met Parametrische Innovatie  Achtergrond en context  Bij Ecotax Filtertechniek wordt al jarenlang gewerkt met bandfilters voor diverse industriële toepassingen. Bandfilters zijn essentieel voor de scheiding van vloeistoffen en vaste stoffen, waarbij elk filter specifiek wordt ontworpen voor de klant. De variatie in debiet (doorstroomcapaciteit), filterdoek, en overige technische eisen resulteert in complexe maatwerkoplossingen. Daarnaast z... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/src/meltano/output\Stageopdrachtomschrijving v1.2 simonboot ecotax filtertechniek.pdf] (SCORE: 9.0000)|||Stageopdracht: Ontwikkeling van een Bandfiltermachine met Parametrische Innovatie  Achtergrond en context  Bij Ecotax Filtertechniek wordt al jarenlang gewerkt met bandfilters voor diverse industriële toepassingen. Bandfilters zijn essentieel voor de scheiding van vloeistoffen en vaste stoffen, waarbij elk filter specifiek wordt ontworpen voor de klant. De variatie in debiet (doorstroomcapaciteit), filterdoek, en overige technische eisen resulteert in complexe maatwerkoplossingen. Daarnaast z... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/src/meltano/output\Stageopdrachtomschrijving v1.2 simonboot ecotax filtertechniek.pdf] (SCORE: 9.0000)|||Stageopdracht: Ontwikkeling van een Bandfiltermachine met Parametrische Innovatie  Achtergrond en context  Bij Ecotax Filtertechniek wordt al jarenlang gewerkt met bandfilters voor diverse industriële toepassingen. Bandfilters zijn essentieel voor de scheiding van vloeistoffen en vaste stoffen, waarbij elk filter specifiek wordt ontworpen voor de klant. De variatie in debiet (doorstroomcapaciteit), filterdoek, en overige technische eisen resulteert in complexe maatwerkoplossingen. Daarnaast z... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/src/meltano/output\Stageopdrachtomschrijving v1.2 simonboot ecotax filtertechniek.pdf] (SCORE: 9.0000)|||productie.  2. Het parametrisch systeem effectief functioneert en in staat is klantspecifieke ontwerpen snel  te genereren.  3. Alle technische berekeningen (zoals assterkte en aandrijvingsspecificaties) correct zijn uitgevoerd en gedocumenteerd.  4. Er een basis elektrisch schakelschema beschikbaar is dat geïntegreerd kan worden in de  productie.  5. Alle documentatie, zoals werktekeningen, handleidingen, en rapportages, volledig en  nauwkeurig is opgeleverd.  Nadruk op technische vaardighed... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/src/meltano/output\Stageopdrachtomschrijving v1.2 simonboot ecotax filtertechniek.pdf] (SCORE: 9.0000)|||productie.  2. Het parametrisch systeem effectief functioneert en in staat is klantspecifieke ontwerpen snel  te genereren.  3. Alle technische berekeningen (zoals assterkte en aandrijvingsspecificaties) correct zijn uitgevoerd en gedocumenteerd.  4. Er een basis elektrisch schakelschema beschikbaar is dat geïntegreerd kan worden in de  productie.  5. Alle documentatie, zoals werktekeningen, handleidingen, en rapportages, volledig en  nauwkeurig is opgeleverd.  Nadruk op technische vaardighed... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/src/meltano/output\Stageopdrachtomschrijving v1.2 simonboot ecotax filtertechniek.pdf] (SCORE: 8.0000)|||productie.  2. Het parametrisch systeem effectief functioneert en in staat is klantspecifieke ontwerpen snel  te genereren.  3. Alle technische berekeningen (zoals assterkte en aandrijvingsspecificaties) correct zijn uitgevoerd en gedocumenteerd.  4. Er een basis elektrisch schakelschema beschikbaar is dat geïntegreerd kan worden in de  productie.  5. Alle documentatie, zoals werktekeningen, handleidingen, en rapportages, volledig en  nauwkeurig is opgeleverd.  Nadruk op technische vaardighed... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/src/meltano/output\Stageopdrachtomschrijving v1.2 simonboot ecotax filtertechniek.pdf] (SCORE: 8.0000)|||Stageopdracht: Ontwikkeling van een Bandfiltermachine met Parametrische Innovatie  Achtergrond en context  Bij Ecotax Filtertechniek wordt al jarenlang gewerkt met bandfilters voor diverse industriële toepassingen. Bandfilters zijn essentieel voor de scheiding van vloeistoffen en vaste stoffen, waarbij elk filter specifiek wordt ontworpen voor de klant. De variatie in debiet (doorstroomcapaciteit), filterdoek, en overige technische eisen resulteert in complexe maatwerkoplossingen. Daarnaast z... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/src/meltano/output\Stageopdrachtomschrijving v1.2 simonboot ecotax filtertechniek.pdf] (SCORE: 7.0000)"
2025-05-29 20:57:54,what is askzaira,24,7,7,10.0000|10.0000|10.0000|9.0000|9.0000|8.0000|7.0000,7,"## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||**Voorbereiding en planning:**     * Inventarisatie van de te koppelen systemen en databronnen.     * Vaststellen van gebruikersrollen en toegangsrechten.     * Opstellen van een projectplan met mijlpalen en deliverables.  2. **Technische setup en datakoppelingen:**     * Inrichten van verbindingen met uw cloudapplicaties (CRM, ERP, SharePoint, Google Drive, etc.).     * Configuratie van de tools aan Askzaira.  3. **Gebruikerstraining en livegang:**     * workshop (online of op locatie) om me... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 8.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 7.0000)"
2025-05-29 21:05:55,what is askzaira,24,7,7,10.0000|10.0000|10.0000|9.0000|9.0000|9.0000|8.0000,7,"## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||**Voorbereiding en planning:**     * Inventarisatie van de te koppelen systemen en databronnen.     * Vaststellen van gebruikersrollen en toegangsrechten.     * Opstellen van een projectplan met mijlpalen en deliverables.  2. **Technische setup en datakoppelingen:**     * Inrichten van verbindingen met uw cloudapplicaties (CRM, ERP, SharePoint, Google Drive, etc.).     * Configuratie van de tools aan Askzaira.  3. **Gebruikerstraining en livegang:**     * workshop (online of op locatie) om me... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 8.0000)"
2025-05-29 21:16:20,wie is zaira,24,7,7,9.0000|9.0000|9.0000|7.0000|7.0000|6.0000|5.0000,7,"## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 7.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 7.0000)|||**Voorbereiding en planning:**     * Inventarisatie van de te koppelen systemen en databronnen.     * Vaststellen van gebruikersrollen en toegangsrechten.     * Opstellen van een projectplan met mijlpalen en deliverables.  2. **Technische setup en datakoppelingen:**     * Inrichten van verbindingen met uw cloudapplicaties (CRM, ERP, SharePoint, Google Drive, etc.).     * Configuratie van de tools aan Askzaira.  3. **Gebruikerstraining en livegang:**     * workshop (online of op locatie) om me... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 6.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 5.0000)"
2025-05-29 21:25:01,askzaira,24,7,7,10.0000|9.0000|9.0000|9.0000|8.0000|8.0000|8.0000,7,"## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||**Voorbereiding en planning:**     * Inventarisatie van de te koppelen systemen en databronnen.     * Vaststellen van gebruikersrollen en toegangsrechten.     * Opstellen van een projectplan met mijlpalen en deliverables.  2. **Technische setup en datakoppelingen:**     * Inrichten van verbindingen met uw cloudapplicaties (CRM, ERP, SharePoint, Google Drive, etc.).     * Configuratie van de tools aan Askzaira.  3. **Gebruikerstraining en livegang:**     * workshop (online of op locatie) om me... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||**Voorbereiding en planning:**     * Inventarisatie van de te koppelen systemen en databronnen.     * Vaststellen van gebruikersrollen en toegangsrechten.     * Opstellen van een projectplan met mijlpalen en deliverables.  2. **Technische setup en datakoppelingen:**     * Inrichten van verbindingen met uw cloudapplicaties (CRM, ERP, SharePoint, Google Drive, etc.).     * Configuratie van de tools aan Askzaira.  3. **Gebruikerstraining en livegang:**     * workshop (online of op locatie) om me... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 8.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 8.0000)|||Pipeliner     Pivotal Tracker     Piwik     Plaid     Planhat     Plausible     Pocket     Polygon Stock API     PostHog     Postmark App     PrestaShop     Pretix     Primetric     Productboard     Productive     Public Apis     PyPI     Qdrant     Qualaroo     QuickBooks     RD Station Marketing     RKI Covid     RSS     RabbitMQ     Railz     Recharge     Recreation     Recruitee     Recurly     Reddit     Redis     RentCast     Repairshopr     Reply.io     Retently     RevenueCat     Revo... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 8.0000)"
2025-05-29 21:35:06,what is askzaira,24,7,7,10.0000|10.0000|9.0000|9.0000|9.0000|8.0000|7.0000,7,"## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 10.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||## Waarom €175 per werknemer per maand niet duur is  De investering voor AskZaira bedraagt €175 per medewerker per maand.    Op het eerste gezicht lijkt dit wellicht een serieuze kostenpost. Toch is het in de praktijk een investering die zichzelf ruimschoots terugverdient — en snel ook.  Hier is waarom:  * Gemiddeld wordt een medewerker 20% productiever door het gebruik van AskZaira.  * Dit betekent dat je in plaats van 6 medewerkers, nog maar 5 medewerkers nodig hebt om hetzelfde werkvolume ... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||**Voorbereiding en planning:**     * Inventarisatie van de te koppelen systemen en databronnen.     * Vaststellen van gebruikersrollen en toegangsrechten.     * Opstellen van een projectplan met mijlpalen en deliverables.  2. **Technische setup en datakoppelingen:**     * Inrichten van verbindingen met uw cloudapplicaties (CRM, ERP, SharePoint, Google Drive, etc.).     * Configuratie van de tools aan Askzaira.  3. **Gebruikerstraining en livegang:**     * workshop (online of op locatie) om me... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 9.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 8.0000)|||* **Snel live:** standaard implementaties kunnen binnen een paar dagen geregeld zijn. vaak niet langer dan 1 week.  * **Minimale inzet nodig van de klantzijde:** alleen selectie van systemen en bronnen vereist.  * **Gebruiksvriendelijk:** geen technische kennis vereist, vragen kunnen gewoon in natuurlijke taal gesteld worden.  **Toegangsmogelijkheden:**  * Microsoft Teams  * WhatsApp  * Discord  * Mobiele apps  * Webinterface   * Desktop Applicatie   * Slack   * Op aanvraag op een ander commu... [SOURCE: C:\Users\<USER>\Documents\AgenticRag\Python\_DATA_RAW\askzaira.txt] (SCORE: 7.0000)"
