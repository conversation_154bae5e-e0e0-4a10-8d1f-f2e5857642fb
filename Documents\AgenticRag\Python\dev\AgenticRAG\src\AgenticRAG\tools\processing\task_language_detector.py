from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorTask_Base
from langchain_core.tools import tool

@tool
def detect_language(query: str, state: SupervisorTaskState):
    """Detects the language of the input text"""
    task = SupervisorManager.get_task("language_detector")
    result = task.model.invoke(f"Detect the language of the following text and return only the language name (e.g., 'English', 'Dutch', 'Spanish', etc.): {query}")
    if isinstance(result, etc.helper_functions.get_any_message_as_type()):
        result = result.content
    return result

async def create_task_language_detector() -> SupervisorTask_SingleAgent:
    return SupervisorManager.register_task(SupervisorTask_SingleAgent(name="language_detector", tools=[], prompt=
                "You are a language detection expert. Your task is to identify the language of the provided text. Return only the language name."))
