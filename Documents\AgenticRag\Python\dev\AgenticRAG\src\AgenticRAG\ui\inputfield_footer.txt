            <div class="security-note">
                <span class="security-icon">🔒</span>
                Al je gegevens worden veilig versleuteld en lokaal opgeslagen
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('setupForm');
        const submitButton = document.getElementById('submitButton');
        const cancelButton = document.getElementById('cancelButton');
        const buttonText = submitButton.querySelector('.button-text');

        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 25;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                
                // Vary particle sizes
                const size = 2 + Math.random() * 4;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                // Vary opacity
                particle.style.opacity = 0.1 + Math.random() * 0.4;
                
                particlesContainer.appendChild(particle);
            }
        }

        // Initialize particles on load
        createParticles();

        // Form validation
        function validateField(field) {
			return true;
            const value = field.value.trim();
            const fieldName = field.name;
            const errorElement = document.getElementById(fieldName + 'Error');
            let isValid = true;

            // Remove previous error state
            field.classList.remove('error');
            errorElement.style.display = 'none';

            switch (fieldName) {
                case 'serverName':
                    if (!value || value.length < 3) {
                        isValid = false;
                        errorElement.textContent = 'Server naam moet minimaal 3 karakters bevatten';
                    }
                    break;
                case 'networkPort':
                    const port = parseInt(value);
                    if (!value || isNaN(port) || port < 1 || port > 65535) {
                        isValid = false;
                        errorElement.textContent = 'Poort moet tussen 1 en 65535 zijn';
                    }
                    break;
                case 'emailAddress':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!value || !emailRegex.test(value)) {
                        isValid = false;
                        errorElement.textContent = 'Vul een geldig email adres in';
                    }
                    break;
                case 'emailPassword':
                    if (!value || value.length < 6) {
                        isValid = false;
                        errorElement.textContent = 'Wachtwoord moet minimaal 6 karakters bevatten';
                    }
                    break;
            }

            if (!isValid) {
                field.classList.add('error');
                errorElement.style.display = 'block';
            }

            return isValid;
        }

        // Real-time validation
        form.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('blur', () => validateField(input));
            input.addEventListener('input', () => {
                if (input.classList.contains('error')) {
                    validateField(input);
                }
            });
        });

		// Form submission
		form.addEventListener('submit', async (e) => {
			e.preventDefault();
			
			// Validate all fields
			const inputs = form.querySelectorAll('.form-input');
			let allValid = true;
			inputs.forEach(input => {
				if (!validateField(input)) {
					allValid = false;
				}
			});
			
			if (!allValid) {
				return;
			}

			// Get identifier from form
			const identifier = document.getElementById('identifier').value;
			if (!identifier) {
				console.error('Identifier is required');
				return;
			}

			// Start loading state
			submitButton.classList.add('button-loading');
			submitButton.disabled = true;
			cancelButton.classList.add('button-loading');
			cancelButton.disabled = true;
			buttonText.textContent = 'Configuratie wordt opgeslagen...';

			try {
				// Prepare form data
				const formData = {};
				inputs.forEach(input => {
					formData[input.name.replace(/\s+/g, '')] = input.value;
				});
				formData.timestamp = new Date().toISOString();

				// Update progress
				buttonText.textContent = 'Verwerken...';

				// Send POST request to aiohttp endpoint
				const response = await fetch(`/${identifier}/oauth_redirect`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(formData)
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				// Update progress
				buttonText.textContent = 'Data opgeslagen...';
				await new Promise(resolve => setTimeout(resolve, 1000));

				const result = await response.json();
				if (result.success === true) {
					buttonText.textContent = 'Voltooid!';

					console.log('Configuration saved:', result);

					// Redirect to dashboard after success
					setTimeout(() => {
						submitButton.classList.remove('button-loading');
						submitButton.disabled = false;
						cancelButton.classList.remove('button-loading');
						cancelButton.disabled = false;
						window.location.href = '/../login'; // Replace with your dashboard URL
					}, 1500);
				}

			} catch (error) {
				console.error('Error submitting form:', error);
				buttonText.textContent = 'Er is een fout opgetreden';
				submitButton.classList.remove('button-loading');
				submitButton.disabled = false;
				cancelButton.classList.remove('button-loading');
				cancelButton.disabled = false;

				setTimeout(() => {
					buttonText.textContent = 'Configuratie Voltooien';
				}, 3000);
			}
		});

        // Autofocus first input
		const firstInput = form.querySelector('.form-input');
		if (firstInput) firstInput.focus();
    </script>
</body>
