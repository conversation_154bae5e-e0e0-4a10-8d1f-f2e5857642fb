            </div>

            <div class="file-upload-section">
                <h3 class="upload-title">Of upload je bestanden</h3>
                <p class="upload-subtitle">Sleep bestanden hiernaartoe of klik om te selecteren</p>
                
                <div class="upload-area" onclick="document.getElementById('file-input').click()">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <span class="primary-text">Klik om bestanden te selecteren</span>
                        <span class="secondary-text">of sleep ze hiernaartoe</span>
                    </div>
                    <div class="supported-formats">PDF, DOC, XLS, CSV, TXT</div>
                </div>
                
                <input type="file" id="file-input" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.txt" style="display: none;">
                
                <div id="file-list" class="file-list"></div>
            </div>

            <button class="connect-button" onclick="processFiles()" id="process-button" style="display: none;">
                <span class="loading"></span>
                <span class="button-text">Bestanden Verwerken</span>
            </button>

            <div class="security-note">
                <span class="security-icon">🔒</span>
                Veilig verbonden via OAuth 2.0 - Jouw gegevens blijven privé
            </div>
			
			<div class="management-buttons">
				<br />
				<br />
				<br />
			</div>
			
			<p><h1>Developer Opties</h1><br /></p>
			<p><form class="restartForm" id="restartForm">
				<div class="form-group">
					<label class="form-label" for="userpass">Wachtwoord:</label>
					<input type="password" id="userpass" name="userpass" class="form-input" placeholder="" required>
					<div class="form-help">Deze knoppen dienen enkel in nood gebruikt te worden</div>
					<div class="form-error" id="serverNameError">Vul een geldige waarde in</div>
				</div>
				<div class="management-buttons">
					<button type="submit" name="action" value="restart" class="management-button" id="restartButton">
						<span class="button-text">Zaira Opnieuw Opstarten</span>
					</button>
					<button type="submit" name="action" value="update" class="management-button" id="updateButton">
						<span class="button-text">Update Omgeving</span>
					</button>
				</div>
			</form></p>
        </div>
    </div>

    <script>
        let uploadedFiles = [];

        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 25;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                
                // Vary particle sizes
                const size = 2 + Math.random() * 4;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                // Vary opacity
                particle.style.opacity = 0.1 + Math.random() * 0.4;
                
                particlesContainer.appendChild(particle);
            }
        }

        // Initialize particles on load
        createParticles();

        // Direct OAuth connection for integrations
        function connectService(url) {
            window.location.href = url;
        }
		
        const form = document.getElementById('restartForm');
		form.addEventListener('submit', async (e) => {
			e.preventDefault()
			
			// Validate all fields
			const inputs = form.querySelectorAll('.form-input');
			const restartButton = document.getElementById('restartButton');
			const updateButton = document.getElementById('updateButton');
			const buttonText1 = restartButton.querySelector('.button-text');
			const buttonText2 = updateButton.querySelector('.button-text');

			// Start loading state
			restartButton.classList.add('button-loading');
			restartButton.disabled = true;
			updateButton.classList.add('button-loading');
			updateButton.disabled = true;
			buttonText1.textContent = 'Wachtwoord...';
			buttonText2.textContent = 'bekijken...';
			
			try {
				
				// Prepare form data
				const formData = {};
				inputs.forEach(input => {
					formData[input.name.replace(/\s+/g, '')] = input.value;
				});
				formData.timestamp = new Date().toISOString();

				// Update progress
				buttonText1.textContent = 'Server...';
				buttonText2.textContent = 'uitschakelen...';

				// Send POST request to aiohttp endpoint
				const response = await fetch(`/restart`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(formData)
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				// Update progress
				buttonText1.textContent = 'Server...';
				buttonText2.textContent = 'opstarten...';
				await new Promise(resolve => setTimeout(resolve, 1000));

				const result = await response.json();
				if (result.success === true) {
					buttonText1.textContent = 'Commando!';
					buttonText2.textContent = 'ontvangen!';

					console.log('Configuration saved:', result);

					// Redirect to dashboard after success
					setTimeout(() => {
						restartButton.classList.remove('button-loading');
						restartButton.disabled = false;
						window.location.href = '/../login'; // Replace with your dashboard URL
					}, 60000);
				}

			} catch (error) {
				console.error('Error submitting form:', error);
				buttonText1.textContent = 'Er is een...';
				buttonText2.textContent = 'fout opgetreden';
				restartButton.classList.remove('button-loading');
				restartButton.disabled = false;

				setTimeout(() => {
					buttonText.textContent = 'Configuratie Voltooien';
				}, 3000);
			}
		})

        // File upload handling
        const fileInput = document.getElementById('file-input');
        const uploadArea = document.querySelector('.upload-area');
        const fileList = document.getElementById('file-list');
        const processButton = document.getElementById('process-button');

        fileInput.addEventListener('change', handleFiles);

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles({ target: { files: e.dataTransfer.files } });
        });

        function handleFiles(event) {
            const files = Array.from(event.target.files);
            
            files.forEach(file => {
                if (!uploadedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    uploadedFiles.push(file);
                }
            });
            
            updateFileList();
            updateProcessButton();
        }

        function updateFileList() {
            if (uploadedFiles.length === 0) {
                fileList.style.display = 'none';
                return;
            }

            fileList.style.display = 'block';
            fileList.innerHTML = uploadedFiles.map((file, index) => `
                <div class="file-item">
                    <div class="file-info">
                        <div class="file-icon">${getFileIcon(file.name)}</div>
                        <div class="file-details">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <button class="remove-file" onclick="removeFile(${index})">Verwijder</button>
                </div>
            `).join('');
        }

        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            updateFileList();
            updateProcessButton();
        }

        function updateProcessButton() {
            if (uploadedFiles.length > 0) {
                processButton.style.display = 'block';
                processButton.querySelector('.button-text').textContent = `${uploadedFiles.length} bestand${uploadedFiles.length > 1 ? 'en' : ''} verwerken`;
            } else {
                processButton.style.display = 'none';
            }
        }

        function getFileIcon(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            const icons = {
                'pdf': 'PDF',
                'doc': 'DOC',
                'docx': 'DOC',
                'xls': 'XLS',
                'xlsx': 'XLS',
                'csv': 'CSV',
                'txt': 'TXT'
            };
            return icons[ext] || 'FILE';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function processFiles() {
            const button = processButton;
            const buttonText = button.querySelector('.button-text');
            
            button.classList.add('button-loading');
            buttonText.textContent = 'Bestanden uploaden...';
            
            // Simulate file processing
            setTimeout(() => {
                buttonText.textContent = 'Verwerken...';
                
                setTimeout(() => {
                    buttonText.textContent = 'Voltooid!';
                    button.classList.remove('button-loading');
                    
                    // Reset after success
                    setTimeout(() => {
                        uploadedFiles = [];
                        updateFileList();
                        updateProcessButton();
                    }, 2000);
                }, 2000);
            }, 1500);
        }

        // Interactive animations for integration cards
        document.addEventListener('mousemove', (e) => {
            const cards = document.querySelectorAll('.integration-item');
            const mouseX = e.clientX;
            const mouseY = e.clientY;
            
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const cardX = rect.left + rect.width / 2;
                const cardY = rect.top + rect.height / 2;
                
                const angleX = (mouseY - cardY) / 30;
                const angleY = (cardX - mouseX) / 30;
                
                card.style.transform = `perspective(1000px) rotateX(${angleX}deg) rotateY(${angleY}deg) translateY(-4px)`;
            });
        });

        document.addEventListener('mouseleave', () => {
            document.querySelectorAll('.integration-item').forEach(card => {
                card.style.transform = '';
            });
        });

        // Add extra visual effects on hover
        document.querySelectorAll('.integration-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 20px 60px rgba(59, 130, 246, 0.4)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });

        // Staggered entrance animation for integration items
        window.addEventListener('load', function() {
            const integrationItems = document.querySelectorAll('.integration-item');
            integrationItems.forEach((item, index) => {
                item.style.setProperty('--index', index);
            });
        });
    </script>
</body>