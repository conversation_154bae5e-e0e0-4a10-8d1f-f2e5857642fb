from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager

class SupervisorTask_HTTP(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        split1 = input_message.split("___Call trace___:", 1)
        split2 = (split1[1].strip() if len(split1) > 1 else input_message).split("___Output message___:", 1)
        output_message = split2[1].strip() if len(split2) > 1 else ''
        user = await ZairaUserManager.find_user(state.user_guid)
        if user.my_task.calling_bot.name == "HTTP":
            await user.my_task.send_response(f"<html><head><title>{user.username}'s response</title></head><body>{output_message}</body></html>")

async def create_out_task_http() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_HTTP(name="http_out",
            prompt="You're a task responsible when the output message needs to be directed to HTTP."))
