from imports import *

import asyncio
import uuid
from typing import List, Dict, Any
from dataclasses import dataclass
from datetime import datetime, timezone
from dotenv import load_dotenv
from qdrant_client.http.models import PointStruct
from qdrant_client.models import SparseVector

from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager
from managers.manager_postgreSQL import PostgreSQLManager
from managers.manager_meltano import MeltanoManager

@dataclass
class ProcessedWooCommerceChunk:
    source_type: str  # e.g., 'product', 'order', 'customer'
    record_id: str
    title: str
    summary: str
    content: str
    metadata: Dict[str, Any]
    embedding: List[float]

class WooCommerceExtractor:
    def __init__(self, consumer_key: str, consumer_secret: str, site_url: str, start_date: str = None, collection_name: str = "mainCollection"):
        load_dotenv()
        self.consumer_key = consumer_key
        self.consumer_secret = consumer_secret
        self.site_url = site_url
        self.start_date = start_date or "2020-01-01"
        self.collection_name = collection_name
        self.qdrant_client = QDrantManager.GetClient()

    @classmethod
    async def setup_async(cls, consumer_key: str, consumer_secret: str, site_url: str, start_date: str = None):
        """Initialize the WooCommerceExtractor class asynchronously"""
        print("=" * 50)
        print("WooCommerce Extractor Setup Started")
        print("=" * 50)
        instance = cls(consumer_key, consumer_secret, site_url, start_date)
        print("WooCommerce extractor setup completed successfully")
        print("=" * 50)
        return instance

    async def run_meltano_extraction(self):
        """Run Meltano tap-woocommerce to extract data to PostgreSQL"""
        try:
            print("Running Meltano WooCommerce extraction...")

            # Determine target based on environment
            target = "target-postgres"
            if not Globals.is_docker():
                target += "-local"

            print(f"Using target: {target}")
            print(f"Docker environment: {Globals.is_docker()}")

            # Run the Meltano extraction command with better error handling
            try:
                result = etc.helper_functions.call_network_docker("meltano", f"run tap-woocommerce {target}")
                print(f"Meltano extraction result: {result}")
                return True
            except Exception as docker_error:
                print(f"Docker command failed: {docker_error}")

                # Try to get more detailed error information
                from subprocess import run as subprocess_run
                try:
                    # Try running the command directly to get stderr
                    network_name = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME", "python")
                    if network_name:
                        container_name = f"{network_name}-meltano"
                        cmd = ["docker", "exec", container_name, "meltano", "run", "tap-woocommerce", target]
                        print(f"Running command: {' '.join(cmd)}")

                        result = subprocess_run(cmd, capture_output=True, text=True, check=False)
                        print(f"Return code: {result.returncode}")
                        print(f"STDOUT: {result.stdout}")
                        print(f"STDERR: {result.stderr}")

                        if result.returncode == 0:
                            return True
                        else:
                            print(f"Meltano command failed with return code {result.returncode}")
                            return False
                    else:
                        print("No ZAIRA_NETWORK_NAME found")
                        return False
                except Exception as subprocess_error:
                    print(f"Subprocess error: {subprocess_error}")
                    return False

        except Exception as e:
            print(f"Error running Meltano extraction: {e}")
            return False

    def format_woocommerce_record(self, record: Dict[str, Any], table_name: str) -> str:
        """Format a WooCommerce record into readable text"""
        try:
            if table_name == "products":
                return self.format_product_record(record)
            elif table_name == "orders":
                return self.format_order_record(record)
            elif table_name == "customers":
                return self.format_customer_record(record)
            else:
                # Generic formatting for other tables
                text_parts = []
                for key, value in record.items():
                    if value is not None and str(value).strip():
                        text_parts.append(f"{key}: {value}")
                return "\n".join(text_parts)
        except Exception as e:
            print(f"Error formatting record: {e}")
            # Fallback to generic formatting
            text_parts = []
            for key, value in record.items():
                if value is not None and str(value).strip():
                    text_parts.append(f"{key}: {value}")
            return "\n".join(text_parts)

    def format_product_record(self, record: Dict[str, Any]) -> str:
        """Format a product record into readable text"""
        parts = []

        # Product basic info
        if record.get('name'):
            parts.append(f"Product Name: {record['name']}")
        if record.get('sku'):
            parts.append(f"SKU: {record['sku']}")
        if record.get('price'):
            parts.append(f"Price: {record['price']}")
        if record.get('description'):
            parts.append(f"Description: {record['description']}")
        if record.get('short_description'):
            parts.append(f"Short Description: {record['short_description']}")
        if record.get('categories'):
            parts.append(f"Categories: {record['categories']}")
        if record.get('tags'):
            parts.append(f"Tags: {record['tags']}")
        if record.get('status'):
            parts.append(f"Status: {record['status']}")
        if record.get('stock_status'):
            parts.append(f"Stock Status: {record['stock_status']}")
        if record.get('stock_quantity'):
            parts.append(f"Stock Quantity: {record['stock_quantity']}")

        return "\n".join(parts)

    def format_order_record(self, record: Dict[str, Any]) -> str:
        """Format an order record into readable text"""
        parts = []

        # Order basic info
        if record.get('id'):
            parts.append(f"Order ID: {record['id']}")
        if record.get('status'):
            parts.append(f"Order Status: {record['status']}")
        if record.get('total'):
            parts.append(f"Total Amount: {record['total']}")
        if record.get('date_created'):
            parts.append(f"Date Created: {record['date_created']}")
        if record.get('customer_id'):
            parts.append(f"Customer ID: {record['customer_id']}")
        if record.get('billing'):
            billing = record['billing']
            if isinstance(billing, dict):
                parts.append(f"Billing Name: {billing.get('first_name', '')} {billing.get('last_name', '')}")
                parts.append(f"Billing Email: {billing.get('email', '')}")
        if record.get('line_items'):
            parts.append(f"Line Items: {record['line_items']}")

        return "\n".join(parts)

    def format_customer_record(self, record: Dict[str, Any]) -> str:
        """Format a customer record into readable text"""
        parts = []

        # Customer basic info
        if record.get('id'):
            parts.append(f"Customer ID: {record['id']}")
        if record.get('email'):
            parts.append(f"Email: {record['email']}")
        if record.get('first_name'):
            parts.append(f"First Name: {record['first_name']}")
        if record.get('last_name'):
            parts.append(f"Last Name: {record['last_name']}")
        if record.get('username'):
            parts.append(f"Username: {record['username']}")
        if record.get('date_created'):
            parts.append(f"Date Created: {record['date_created']}")
        if record.get('orders_count'):
            parts.append(f"Orders Count: {record['orders_count']}")
        if record.get('total_spent'):
            parts.append(f"Total Spent: {record['total_spent']}")

        return "\n".join(parts)

    async def get_title_and_summary(self, content: str, source_type: str, record_id: str) -> Dict[str, str]:
        """Generate title and summary for WooCommerce content"""
        try:
            # Create context-aware titles based on source type
            if source_type == "products":
                title = f"WooCommerce Product {record_id}"
            elif source_type == "orders":
                title = f"WooCommerce Order {record_id}"
            elif source_type == "customers":
                title = f"WooCommerce Customer {record_id}"
            else:
                title = f"WooCommerce {source_type} {record_id}"

            # Generate summary from first 200 characters
            summary = content[:200] + "..." if len(content) > 200 else content

            return {
                "title": title,
                "summary": summary
            }
        except Exception as e:
            print(f"Error generating title and summary: {e}")
            return {
                "title": f"WooCommerce {source_type} {record_id}",
                "summary": content[:200] + "..."
            }

    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding vector for text"""
        try:
            embeddings = ZairaSettings.OllamaSettings().embed_model.get_text_embedding(text)
            return embeddings
        except Exception as e:
            print(f"Error getting embedding: {e}")
            return [0] * EMBEDDING_SIZE  # Return zero vector on error

    async def process_chunk(self, chunk: str, chunk_number: int, source_type: str, record_id: str, table_name: str) -> ProcessedWooCommerceChunk:
        """Process a single chunk of WooCommerce data"""
        try:
            # Get title and summary
            extracted = await self.get_title_and_summary(chunk, source_type, record_id)

            # Get embedding
            embedding = await self.get_embedding(chunk)

            # Create metadata
            metadata = {
                "source": "woocommerce_extraction",
                "source_type": source_type,
                "table_name": table_name,
                "record_id": record_id,
                "chunk_number": chunk_number,
                "chunk_size": len(chunk),
                "extracted_at": datetime.now(timezone.utc).isoformat(),
                "site_url": self.site_url
            }

            return ProcessedWooCommerceChunk(
                source_type=source_type,
                record_id=record_id,
                title=extracted['title'],
                summary=extracted['summary'],
                content=chunk,
                metadata=metadata,
                embedding=embedding
            )
        except Exception as e:
            print(f"Error processing chunk: {e}")
            return None

    async def insert_chunk(self, chunk: ProcessedWooCommerceChunk):
        """Insert a processed chunk into Qdrant"""
        try:
            embedded_sparse = await RetrievalManager.get_embeddings_sparse(chunk.content)
            sparse_vector = SparseVector(
                indices=embedded_sparse.indices,
                values=embedded_sparse.values
            )

            # Create point with required ID and properly structured metadata
            point = PointStruct(
                id=uuid.uuid4().int & (1<<64)-1,  # Generate integer ID from UUID
                vector={"text-dense": chunk.embedding, "text-sparse": sparse_vector},
                payload={
                    "source_type": chunk.source_type,
                    "record_id": chunk.record_id,
                    "title": chunk.title,
                    "summary": chunk.summary,
                    "content": chunk.content,
                    "extracted_at": chunk.metadata.get("extracted_at"),
                    "site_url": chunk.metadata.get("site_url"),
                    "source": chunk.metadata.get("source"),
                    "table_name": chunk.metadata.get("table_name"),
                    "chunk_number": chunk.metadata.get("chunk_number"),
                    "chunk_size": chunk.metadata.get("chunk_size")
                }
            )

            # Use upsert instead of insert for better reliability
            self.qdrant_client.upsert(collection_name=self.collection_name, points=[point])
            print(f"Successfully inserted chunk {chunk.metadata.get('chunk_number')} for {chunk.source_type} {chunk.record_id}")
        except Exception as e:
            print(f"Error inserting chunk: {e}")
            raise

    async def process_and_store_record(self, record: Dict[str, Any], table_name: str, record_index: int):
        """Process a WooCommerce record and store its chunks"""
        try:
            # Format the record into readable text
            formatted_content = self.format_woocommerce_record(record, table_name)

            # Chunk the content using RetrievalManager's chunk_text method
            # This uses LangChain's RecursiveCharacterTextSplitter with text-structured based splitting
            chunks = await RetrievalManager.chunk_text(
                data=formatted_content,
                chunk_size=1000,  # Default chunk size
                chunk_overlap=200  # Default overlap for semantic coherence
            )

            # Determine source type and record ID
            source_type = table_name
            record_id = str(record.get('id', record_index))

            # Process chunks in parallel
            tasks = [
                self.process_chunk(chunk, i, source_type, record_id, table_name)
                for i, chunk in enumerate(chunks)
            ]
            processed_chunks = await asyncio.gather(*tasks)

            # Store chunks in parallel
            insert_tasks = [
                self.insert_chunk(chunk)
                for chunk in processed_chunks if chunk is not None
            ]
            await asyncio.gather(*insert_tasks)

        except Exception as e:
            print(f"Error processing record {record_index} from {table_name}: {e}")

    async def extract_and_store_data(self):
        """Main method to extract WooCommerce data and store in vector database"""
        try:
            print("Starting WooCommerce data extraction and storage...")

            # Step 1: Try Meltano extraction first
            meltano_success = await self.run_meltano_extraction()
            await MeltanoManager.ConvertSQLToVectorStore()

            # if meltano_success:
            #     print("Meltano extraction successful, processing data from PostgreSQL...")

            #     # Step 2: Process data from PostgreSQL similar to MeltanoManager.ConvertSQLToVectorStore
            #     await PostgreSQLManager.connect_to_database("meltanodb")

            #     # Get table names
            #     table_names = await PostgreSQLManager.get_table_names("meltanodb")
            #     print(f"Found tables: {table_names}")

            #     # Process each table
            #     for table_name in table_names:
            #         print(f"Processing table: {table_name}")
            #         raw_data = await PostgreSQLManager.execute_query("meltanodb", f"SELECT * FROM public.{table_name}")

            #         if raw_data:
            #             print(f"Found {len(raw_data)} records in {table_name}")

            #             # Process records in batches to avoid overwhelming the system
            #             batch_size = 10
            #             for i in range(0, len(raw_data), batch_size):
            #                 batch = raw_data[i:i + batch_size]
            #                 tasks = [
            #                     self.process_and_store_record(dict(record), table_name, record_index + i)
            #                     for record_index, record in enumerate(batch)
            #                 ]
            #                 await asyncio.gather(*tasks)
            #                 print(f"Processed batch {i//batch_size + 1} of {(len(raw_data) + batch_size - 1)//batch_size} for {table_name}")
            #         else:
            #             print(f"No data found in table {table_name}")

            #     await PostgreSQLManager.close_connection("meltanodb")
            #     print("WooCommerce data extraction and storage completed successfully!")
            return True
            # else:
            #     print("Meltano extraction failed. This might be due to:")
            #     print("1. tap-woocommerce not being installed in Meltano")
            #     print("2. Missing or incorrect WooCommerce credentials")
            #     print("3. WooCommerce API connection issues")
            #     print("4. Meltano configuration issues")
            #     print("\nTo fix this, you may need to:")
            #     print("- Install tap-woocommerce in your Meltano project")
            #     print("- Verify your WooCommerce API credentials")
            #     print("- Check your WooCommerce site URL and API access")

            #     # For now, we'll return False, but in the future we could implement
            #     # direct WooCommerce API extraction as a fallback
            #     return False

        except Exception as e:
            print(f"Error during WooCommerce extraction and storage: {e}")
            import traceback
            traceback.print_exc()
            return False
