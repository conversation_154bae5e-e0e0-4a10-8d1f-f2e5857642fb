<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Setup - AskZaira</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #000000;
            color: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* Animated background elements */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
            animation: pulse 4s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.6; }
        }

        /* Floating particles animation */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
            z-index: 0;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            animation: float 15s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        .container {
            width: 100%;
            max-width: 720px;
            margin: 0 auto;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .logo {
            width: 56px;
            height: 56px;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
            border-radius: 16px 16px 0 0;
        }

        .brand-name {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tagline {
            color: #94a3b8;
            font-size: 1rem;
        }

        .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 24px;
            padding: 3rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
            border-radius: 24px 24px 0 0;
        }

        .card-header {
            text-align: center;
            margin-bottom: 2.5rem;
            position: relative;
            z-index: 1;
        }

        .card-title {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-subtitle {
            color: #94a3b8;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .form-container {
            position: relative;
            z-index: 1;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: #e2e8f0;
            margin-bottom: 0.75rem;
        }

        .form-input {
            width: 100%;
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(71, 85, 105, 0.4);
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            color: #f8fafc;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            outline: none;
            border-color: rgba(59, 130, 246, 0.6);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(30, 41, 59, 0.9);
        }

        .form-input::placeholder {
            color: #64748b;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .submit-button {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border: none;
            border-radius: 16px;
            padding: 1.25rem 2rem;
            font-size: 1.125rem;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
            margin-top: 1rem;
        }

        .submit-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
        }

        .submit-button:hover::before {
            left: 100%;
        }

        .submit-button:active {
            transform: translateY(0);
        }

        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .button-loading .loading {
            display: inline-block;
        }

        .button-loading {
            pointer-events: none;
        }

        .security-note {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 12px;
            text-align: center;
            font-size: 0.875rem;
            color: #86efac;
        }

        .security-icon {
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        .progress-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
        }

        .progress-step {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(71, 85, 105, 0.4);
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
        }

        .progress-step.completed {
            background: rgba(34, 197, 94, 0.8);
        }

        .progress-line {
            width: 40px;
            height: 2px;
            background: rgba(71, 85, 105, 0.4);
            margin: 0 0.25rem;
            transition: all 0.3s ease;
        }

        .progress-line.completed {
            background: rgba(34, 197, 94, 0.6);
        }

        .form-help {
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 0.5rem;
            line-height: 1.4;
        }

        .form-error {
            font-size: 0.875rem;
            color: #fca5a5;
            margin-top: 0.5rem;
            display: none;
        }

        .form-input.error {
            border-color: rgba(239, 68, 68, 0.6);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        @media (max-width: 640px) {
            .container {
                padding: 1rem;
            }
            
            .card {
                padding: 2rem 1.5rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .brand-name {
                font-size: 1.75rem;
            }

            .card-title {
                font-size: 1.5rem;
            }
        }

        /* Input focus animations */
        .form-input:focus + .form-label::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            animation: expandWidth 0.3s ease;
        }

        @keyframes expandWidth {
            0% { width: 0; }
            100% { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>
    
    <div class="container">
        <div class="logo-section">
            <div class="logo">AZ</div>
            <h1 class="brand-name">AskZaira</h1>
            <p class="tagline">Configureer je server instellingen</p>
        </div>

        <div class="card">
            <div class="progress-indicator">
                <div class="progress-step completed"></div>
                <div class="progress-line completed"></div>
                <div class="progress-step active"></div>
                <div class="progress-line"></div>
                <div class="progress-step"></div>
            </div>

            <div class="card-header">
                <h2 class="card-title">Server Configuratie</h2>
                <p class="card-subtitle">Voer je server- en email instellingen in om door te gaan naar het dashboard</p>
            </div>

            <form class="form-container" id="setupForm">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label" for="serverName">Server Naam</label>
                        <input 
                            type="text" 
                            id="serverName" 
                            name="serverName" 
                            class="form-input" 
                            placeholder="bijv. hoofdserver.bedrijf.nl"
                            required
                        >
                        <div class="form-help">De naam van je server of hostname</div>
                        <div class="form-error" id="serverNameError">Vul een geldige server naam in</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="networkPort">Netwerk Poort</label>
                        <input 
                            type="number" 
                            id="networkPort" 
                            name="networkPort" 
                            class="form-input" 
                            placeholder="8080"
                            min="1"
                            max="65535"
                            required
                        >
                        <div class="form-help">Poort nummer (1-65535)</div>
                        <div class="form-error" id="networkPortError">Vul een geldig poort nummer in</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="emailAddress">E-mail Adres</label>
                    <input 
                        type="email" 
                        id="emailAddress" 
                        name="emailAddress" 
                        class="form-input" 
                        placeholder="<EMAIL>"
                        required
                    >
                    <div class="form-help">Je primaire email adres voor systeem notificaties</div>
                    <div class="form-error" id="emailAddressError">Vul een geldig email adres in</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="emailPassword">E-mail Wachtwoord</label>
                    <input 
                        type="password" 
                        id="emailPassword" 
                        name="emailPassword" 
                        class="form-input" 
                        placeholder="••••••••••••"
                        required
                    >
                    <div class="form-help">Je email wachtwoord voor SMTP configuratie</div>
                    <div class="form-error" id="emailPasswordError">Wachtwoord is verplicht</div>
                </div>

                <button type="submit" class="submit-button" id="submitButton">
                    <span class="loading"></span>
                    <span class="button-text">Configuratie Voltooien</span>
                </button>
            </form>

            <div class="security-note">
                <span class="security-icon">🔒</span>
                Al je gegevens worden veilig versleuteld en lokaal opgeslagen
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('setupForm');
        const submitButton = document.getElementById('submitButton');
        const buttonText = submitButton.querySelector('.button-text');

        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 25;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                
                // Vary particle sizes
                const size = 2 + Math.random() * 4;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                // Vary opacity
                particle.style.opacity = 0.1 + Math.random() * 0.4;
                
                particlesContainer.appendChild(particle);
            }
        }

        // Initialize particles on load
        createParticles();

        // Form validation
        function validateField(field) {
            const value = field.value.trim();
            const fieldName = field.name;
            const errorElement = document.getElementById(fieldName + 'Error');
            let isValid = true;

            // Remove previous error state
            field.classList.remove('error');
            errorElement.style.display = 'none';

            switch (fieldName) {
                case 'serverName':
                    if (!value || value.length < 3) {
                        isValid = false;
                        errorElement.textContent = 'Server naam moet minimaal 3 karakters bevatten';
                    }
                    break;
                case 'networkPort':
                    const port = parseInt(value);
                    if (!value || isNaN(port) || port < 1 || port > 65535) {
                        isValid = false;
                        errorElement.textContent = 'Poort moet tussen 1 en 65535 zijn';
                    }
                    break;
                case 'emailAddress':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!value || !emailRegex.test(value)) {
                        isValid = false;
                        errorElement.textContent = 'Vul een geldig email adres in';
                    }
                    break;
                case 'emailPassword':
                    if (!value || value.length < 6) {
                        isValid = false;
                        errorElement.textContent = 'Wachtwoord moet minimaal 6 karakters bevatten';
                    }
                    break;
            }

            if (!isValid) {
                field.classList.add('error');
                errorElement.style.display = 'block';
            }

            return isValid;
        }

        // Real-time validation
        form.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('blur', () => validateField(input));
            input.addEventListener('input', () => {
                if (input.classList.contains('error')) {
                    validateField(input);
                }
            });
        });

        // Form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Validate all fields
            const inputs = form.querySelectorAll('.form-input');
            let allValid = true;

            inputs.forEach(input => {
                if (!validateField(input)) {
                    allValid = false;
                }
            });

            if (!allValid) {
                return;
            }

            // Start loading state
            submitButton.classList.add('button-loading');
            submitButton.disabled = true;
            buttonText.textContent = 'Configuratie wordt opgeslagen...';

            // Simulate API call
            try {
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Update progress
                buttonText.textContent = 'Verbinding testen...';
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                buttonText.textContent = 'Voltooid!';
                
                // Save form data to localStorage for demo purposes
                const formData = {
                    serverName: document.getElementById('serverName').value,
                    networkPort: document.getElementById('networkPort').value,
                    emailAddress: document.getElementById('emailAddress').value,
                    timestamp: new Date().toISOString()
                };
                
                // In a real application, you would send this data to your server
                console.log('Configuration saved:', formData);
                
                // Redirect to dashboard after success
                setTimeout(() => {
                    window.location.href = 'dashboard.html'; // Replace with your dashboard URL
                }, 1000);
                
            } catch (error) {
                buttonText.textContent = 'Er is een fout opgetreden';
                submitButton.classList.remove('button-loading');
                submitButton.disabled = false;
                
                setTimeout(() => {
                    buttonText.textContent = 'Configuratie Voltooien';
                }, 3000);
            }
        });

        // Auto-focus first input
        document.getElementById('serverName').focus();
    </script>
</body>
</html>