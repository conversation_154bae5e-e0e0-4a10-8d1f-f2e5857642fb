from imports import *

from asyncio import create_task, sleep, Task
from pydantic import BaseModel
from typing import  Any, Optional
from pydantic import ConfigDict

class MyBot_Generic(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.LongRunningZairaTask import LongRunningZairaTask
    parent_instance: Optional[Any] = None
    name: str = ""
    asyncio_Task: Optional[Task] = None # Python-only

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, parent_instance, name, **kwargs):
        super().__init__(parent_instance=parent_instance,name=name,asyncio_Task=None,**kwargs)

    async def on_ready(self):
        await self.send_broadcast("Gegroet collega's! Hoe mag ik u vandaag gehoorzamen? Om zeker te zijn dat ik weet dat je het tegen mij hebt hoef je enkel en alleen je berichten met een '!' te beginnen.")

    async def on_message(self, text: str, message):
        pass

    async def on_member_join(self, member_name: str, message):
        from endpoints.teams_endpoint import MyTeamsBot
        from endpoints.discord_endpoint import MyDiscordBot
        from endpoints.whatsapp_endpoint import MyWhatsappBot

        welcome_text = f"Hello and welcome {member_name}!"
        if self.parent_instance == None:
            # Python call
            print("HACKING ATTEMPT! Should never occur!")
        elif isinstance(self.parent_instance, MyTeamsBot):
            await message.send_activity(welcome_text)
        elif isinstance(self.parent_instance, MyDiscordBot):
            await message.create_dm()
            await message.dm_channel.send(welcome_text)
        elif isinstance(self.parent_instance, MyWhatsappBot):
            # For WhatsApp, message would be the phone number
            await MyWhatsappBot.send_a_whatsapp_message(message, welcome_text)

    async def request_human_in_the_loop_internal(self, request:str, task: "LongRunningZairaTask", message, halt_until_response = False):
        if self.name == "Python":
            result = input(request)
            await task.user.on_message(result, self, [], task.original_physical_message)
        elif self.name == "Discord":
            # # If wait is requested
            # def check(m):
            #     # Check if the reply is from the same user in the same channel
            #     return m.author == message.author and m.channel == message.channel

            # async def handle_query():
            #     try:
            #         result = await MyDiscordBot.bot.wait_for('message', check=check, timeout=360)
            #         #await self.send_reply(f'You replied: "{reply.content}" within 6 minutes! Nice!', message)
            #         #callback(result) is handled inside start_task(), so simply ignore the result and return the function
            #     except TimeoutError:
            #         await self.send_reply(f'{message.author.mention}, time\’s up! You didn\’t reply within 6 minutes.', message)
            # MyDiscordBot.bot.loop.create_task(handle_query())

            await self.send_reply(request, task, message)
        elif self.name == "Teams":
            await self.send_reply(request, task, message)
        elif self.name == "Whatsapp":
            await self.send_reply(request, task, message)
        if halt_until_response == True:
            while True:
                await sleep(1)
                if task.human_in_the_loop_callback == None:
                    break
    
    async def send_broadcast(self, text):
        if self.name == "Python":
            print(text)
        elif self.name == "Discord":
            from endpoints.discord_endpoint import MyDiscordBot
            await MyDiscordBot.send_discord_broadcast(text)

    async def send_reply(self, text: str, task: "LongRunningZairaTask", physical_message):
        if self.name == "Python":
            async def wait_for_no_task(self: "MyBot_Generic", text):
                while True:
                   # Wait with outputting to the Python log until there's nothing being logged anymore
                   await sleep(1)
                   if task.user.my_task == None:
                       break
                print(text)
                print("")
            self.asyncio_Task = create_task(wait_for_no_task(self, text))
            self.asyncio_Task.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
        elif self.name == "Teams":
            await physical_message.send_activity(text)
        elif self.name == "Discord":
            chunks = [text[i:i+2000] for i in range(0, len(text), 2000)]
            for chunk in chunks:
                await physical_message.reply(chunk)
        elif self.name == "Whatsapp":
            from endpoints.whatsapp_endpoint import MyWhatsappBot
            # For WhatsApp, physical_message contains the sender's phone number
            sender_id = physical_message
            chunks = [text[i:i+1000] for i in range(0, len(text), 1000)]  # WhatsApp has smaller message limits
            for chunk in chunks:
                await MyWhatsappBot.send_a_whatsapp_message(sender_id, chunk)
        task.user.chat_history.append(f"Zaira: {text}")
