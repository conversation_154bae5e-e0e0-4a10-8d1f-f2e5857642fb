import sys
import os
import asyncio
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

async def send_test_email():
    """
    Send a test email directly using SMTP without relying on the imports module
    """
    # SMTP Configuration
    smtp_server = "smtp-mail.outlook.com"  # Outlook SMTP server
    smtp_port = 587  # Outlook SMTP port for TLS
    smtp_username = "<EMAIL>"  # Your email
    smtp_password = "bfhcgsvztdzbptta"  # Your password
    sender = smtp_username
    recipient = "<EMAIL>"
    subject = "Test Email from AgenticRAG"
    content = "This is a test email from the AgenticRAG system."
    
    # Create a MIME message
    msg = MIMEMultipart()
    msg['From'] = sender
    msg['To'] = recipient
    msg['Subject'] = subject
    
    # Attach the email body
    msg.attach(MIMEText(content, 'plain'))
    
    try:
        # Connect to the SMTP server
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()  # Secure the connection
        server.login(smtp_username, smtp_password)
        
        # Send the email
        server.send_message(msg)
        server.quit()
        print(f"Email sent to {recipient} with subject: {subject}")
    except Exception as e:
        print(f"Failed to send email: {str(e)}")

if __name__ == "__main__":
    asyncio.run(send_test_email())
