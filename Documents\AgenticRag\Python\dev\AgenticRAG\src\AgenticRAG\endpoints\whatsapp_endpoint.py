from imports import *

import threading
import asyncio
from aiohttp import web
import json
from urllib.parse import parse_qs
import httpx
import os
from json import JSONDecodeError

from endpoints.api_endpoint import APIEndpoint
from managers.manager_users import ZairaUserManager
from userprofiles.permission_levels import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic
from config import (
    META_APP_ID,
    META_APP_SECRET,
    WHATSAPP_PHONE_NUMBER_ID,
    WHATSAPP_BUSINESS_ACCOUNT_ID,
    WHATSAPP_VERIFY_TOKEN,
    WHATSAPP_ACCESS_TOKEN
)

#runnning on ngrok with webhook url for now 2
#Web Interface   http://127.0.0.1:4040  Forwarding  https://5a31-178-228-207-28.ngrok-free.app -> http://localhost:80

class MyWhatsAppClient:
    async def on_ready(self):
        await MyWhatsappBot.on_ready()

    async def process_webhook(self, data):
        await MyWhatsappBot.process_webhook(data)

class MyWhatsappBot:
    _instance = None
    _initialized = False
    bot_generic: MyBot_Generic = None

    # WhatsApp Bot client
    client = MyWhatsAppClient()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        if Globals.get_debug() == True:
            return

        # Get the current event loop or create a new one
        instance.loop = asyncio.new_event_loop()

        # Function to run in thread
        def run_bot_in_loop():
            asyncio.set_event_loop(instance.loop)  # Set the loop for this thread
            instance.loop.run_until_complete(MyWhatsappBot.run_whatsapp_bot_internal())

        # Add routes to the API endpoint for WhatsApp webhooks
        APIEndpoint.get_instance().aio_app.add_routes([
            web.post('/whatsapp/webhook', instance.whatsapp_webhook),
            web.get('/whatsapp/webhook', instance.whatsapp_verify),
        ])

        # Create and start the thread
        instance.bot_thread = threading.Thread(target=run_bot_in_loop)
        instance.bot_thread.daemon = True  # Daemonize thread so it exits when main thread exits
        instance.bot_thread.start()
        instance.bot_generic = MyBot_Generic(instance, "Whatsapp")

        instance._initialized = True

    @classmethod
    async def on_ready(cls):
        print("WhatsApp bot initialized and ready to receive messages!")

    @classmethod
    async def process_webhook(cls, data):
        """
        Process incoming WhatsApp webhook data.
        """
        try:
            # Extract the message data - this structure will vary based on the WhatsApp API you use
            # This is based on the Meta Cloud API format
            if 'entry' in data and len(data['entry']) > 0:
                for entry in data['entry']:
                    if 'changes' in entry and len(entry['changes']) > 0:
                        for change in entry['changes']:
                            if change.get('field') == 'messages':
                                if 'value' in change and 'messages' in change['value']:
                                    for message in change['value']['messages']:
                                        # Extract message details
                                        message_id = message.get('id')
                                        from_number = message.get('from')

                                        # Extract message text
                                        message_text = ""
                                        if 'text' in message and 'body' in message['text']:
                                            message_text = message['text']['body']

                                        # Process the message
                                        await cls.on_message(message_text, from_number, message)
        except Exception as error:
            etc.helper_functions.exception_triggered(error)

    @classmethod
    async def on_message(cls, text: str, sender_id: str, original_message):
        """
        Handle incoming messages from WhatsApp.
        """
        if len(text) <= 1:
            return

        print(f"Received WhatsApp message: {text} from {sender_id}")

        # Find or create a user for this sender
        user = await ZairaUserManager.get_user(sender_id)
        if user is None:
            # Temporarily create a new user if it's the first message since the server was started
            user = await ZairaUserManager.add_user(
                sender_id,
                PERMISSION_LEVELS.USER,
                ZairaUserManager.create_guid(),
                ZairaUserManager.create_guid()
            )

        async def handle_command():
            print(f'WhatsApp: {sender_id}: "{text}"')
            attachments = []
            await user.on_message(complete_message=text, calling_bot=cls.get_instance().bot_generic, attachments=attachments, original_message=sender_id)

        if user is not None:
            await handle_command()

    @classmethod
    async def send_a_whatsapp_message(cls, to_number: str, message: str) -> bool:
        """
        Send a message to a WhatsApp user using the Meta Cloud API.
        """
        if not message:
            print('(Message was empty)')
            return False

        try:
            # Make sure we have the required configuration
            if not WHATSAPP_PHONE_NUMBER_ID:
                print("ERROR: WhatsApp Phone Number ID not configured")
                return False

            # Use the access token directly
            access_token = WHATSAPP_ACCESS_TOKEN

            # Prepare the API endpoint
            url = f"https://graph.facebook.com/v18.0/{WHATSAPP_PHONE_NUMBER_ID}/messages"

            # Prepare the message payload
            payload = {
                "messaging_product": "whatsapp",
                "recipient_type": "individual",
                "to": to_number,
                "type": "text",
                "text": {
                    "body": message
                }
            }

            # Set up headers with the access token
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}"
            }

            # Send the message
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, headers=headers)

                if response.status_code == 200:
                    print(f"WhatsApp message sent successfully to {to_number}")
                    return True
                else:
                    print(f"Failed to send WhatsApp message. Status code: {response.status_code}")
                    print(f"Response: {response.text}")
                    return False

        except Exception as e:
            print(f"Error sending WhatsApp message: {e}")
            return False

    @classmethod
    async def run_whatsapp_bot_internal(cls):
        try:
            # Initialize the WhatsApp bot
            await cls.client.on_ready()
        except Exception as e:
            print(f"Error running WhatsApp bot: {e}")

    # API Endpoints and Webhook handlers
    async def whatsapp_verify(self, request: web.Request) -> web.Response:
        """
        Handle the verification request from WhatsApp.
        WhatsApp sends a GET request with a hub.challenge parameter when you set up a webhook.
        """
        print(f"WhatsApp verification request received from IP: {request.remote}")

        # Get query parameters
        query_params = parse_qs(request.query_string)

        # WhatsApp sends these verification parameters
        mode = query_params.get('hub.mode', [None])[0]
        token = query_params.get('hub.verify_token', [None])[0]
        challenge = query_params.get('hub.challenge', [None])[0]

        # Verify token using the one defined at the top of the file
        if mode == 'subscribe' and token == WHATSAPP_VERIFY_TOKEN and challenge:
            print("WhatsApp webhook verified successfully")
            return web.Response(text=challenge)
        else:
            print(f"WhatsApp webhook verification failed. Mode: {mode}, Token: {token}, Challenge: {challenge}")
            return web.Response(status=403)

    async def whatsapp_webhook(self, request: web.Request) -> web.Response:
        """
        Handle incoming messages from WhatsApp.
        """
        try:
            if request.content_type != 'application/json':
                return web.Response(status=415)

            # Parse the incoming JSON payload
            body = await request.json()
            print(f"Received WhatsApp webhook: {body}")

            # Process the message in the bot's thread
            await MyWhatsappBot.client.process_webhook(body)

            # WhatsApp expects a 200 OK response
            return web.Response(status=200)

        except JSONDecodeError:
            print("Invalid JSON in webhook request")
            return web.Response(status=400)
        except Exception as error:
            etc.helper_functions.exception_triggered(error)
            return web.Response(status=500)